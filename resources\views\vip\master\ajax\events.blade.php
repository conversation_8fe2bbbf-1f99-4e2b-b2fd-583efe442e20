@if ($events->count() > 0)
    <div class="events-grid">
        @foreach ($events as $event)
            <a href="{{ route('event.detailEvent', $event->slug) }}" class="event-card group cursor-pointer">
                <div class="event-image bg-cover"
                    style="background-image: url('{{ strpos($event->gambar, 'http') === 0 ? $event->gambar : asset($event->gambar) }}');">
                    <div class="event-date">
                        <span class="event-day">{{ \Carbon\Carbon::parse($event->tanggal)->format('d') }}</span>
                        <span class="event-month">{{ \Carbon\Carbon::parse($event->tanggal)->format('M') }}</span>
                    </div>
                </div>
                <div class="event-info">
                    <h3 class="text-white font-bold text-lg mb-1">{{ $event->nama }}</h3>
                    <p class="text-gray-400 text-sm mb-1">{{ $event->nama_kota }}</p>
                    <p class="text-gray-500 text-xs">{{ \Carbon\Carbon::parse($event->tanggal)->format('d M Y') }}</p>
                </div>
            </a>
        @endforeach
    </div>
@else
    <p class="text-center text-gray-400">No events found.</p>
@endif
