<?php

use Illuminate\Foundation\Application;
use App\Http\Middleware\AuthMiddleware;
use App\Http\Middleware\GuestMiddleware;
use App\Http\Middleware\AdminMiddleware;
use App\Http\Middleware\RedirectIfAdmin;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->alias([
            // AuthMiddleware::class => 'custom-auth',
            // GuestMiddleware::class => 'custom-guest',
            'custom-auth' => AuthMiddleware::class,
            'custom-guest' => GuestMiddleware::class,
            'custom-admin' => AdminMiddleware::class,
            'redirect-if-admin' => RedirectIfAdmin::class,
        ]);
        $middleware->validateCsrfTokens(except: [
            'xendit/callback-id',
            'xendit/callback-my',
            'xendit/callback-th',
            'xendit/callback-ph',
            'xendit/callback-vn',
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
