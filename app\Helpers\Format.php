<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Log;

class Format
{
    public static function formatPhone($phone, $country = 'ID')
    {
        if ($country == 'ID') {
            $phone = str_replace(' ', '', $phone);
            if (substr($phone, 0, 3) == '+62' && substr($phone, 3, 1) == '0') {
                $phone = '+62' . substr($phone, 4);
            }
            if (substr($phone, 0, 1) == '0') {
                $phone = '+62' . substr($phone, 1);
            }
            return $phone;
        } else {
            return $phone;
        }
    }

    public static function formatCurrency($nominal, $currency)
    {

        return $currency === 'IDR'
            ? $currency . ' ' . number_format($nominal, 0)
            : $currency . ' ' . number_format($nominal, 2);

    }
}
