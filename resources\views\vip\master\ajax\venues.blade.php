@if ($venues->count() > 0)
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    @foreach ($venues as $venue)
    <a href="{{ route('venue.detailVenue', $venue->slug) }}" class="venue-item-simple group cursor-pointer">
        <div class="venue-image-golden kreen-golden-border"
            style="background-image: url('{{ strpos($venue->banner, 'http') === 0 ? $venue->banner : asset($venue->banner) }}');">
        </div>
        <div class="venue-info-simple">
            <h3 class="text-white font-bold text-lg mb-1">{{ $venue->nama }}</h3>
            <div class="flex items-center text-gray-400 text-sm">
                <svg class="w-4 h-4 text-kreen-gold mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                        clip-rule="evenodd" />
                </svg>
                {{ $venue->nama_kota }}
            </div>
        </div>
    </a>
    @endforeach
</div>
@else
    <p class="text-center text-gray-400">No venues found.</p>
@endif
