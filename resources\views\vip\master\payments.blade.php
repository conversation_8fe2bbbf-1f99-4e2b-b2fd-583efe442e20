@extends('vip.app')

@section('title', 'Payment - KREEN VIP')

@push('styles')
<style>
.sticky-card {
    position: sticky;
    top: 10rem;
    z-index: 10;
}

.booking-container {
    position: relative;
    overflow: visible;
}

.booking-grid {
    position: relative;
    overflow: visible;
}

/* Full width section */
.full-width-section {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    padding-left: calc(50vw - 50%);
    padding-right: calc(50vw - 50%);
}

/* Header with background image */
.header-bg {
    background-image: url('https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    position: relative;
}

.header-bg::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5));
    z-index: 1;
}

.header-content {
    position: relative;
    z-index: 2;
}

/* Mobile sticky adjustments */
@media (max-width: 1024px) {
    .sticky-card {
        position: relative;
        top: auto;
    }
}

/* Progress indicator responsive design */
@media (max-width: 768px) {
    .booking-progress {
        display: none !important;
    }
    
    .booking-progress-mobile {
        display: flex !important;
        justify-content: center;
        padding: 0.5rem 0;
        border-top: 1px solid #374151;
        margin-top: 1rem;
    }
}

@media (min-width: 769px) {
    .booking-progress-mobile {
        display: none !important;
    }
}

/* Timer animation */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.timer-pulse {
    animation: pulse 2s infinite;
}

/* Copy button hover effect */
.copy-btn:hover {
    background-color: rgba(163, 122, 29, 0.1);
}
</style>
@endpush

@section('content')
<!-- Full Width Header with Background Image -->
<section class="full-width-section header-bg py-16">
    <div class="header-content text-center">
        <h1 class="text-4xl md:text-6xl font-bold text-white mb-4">HELEN'S NIGHT MART <span class="text-purple-400">GADING SERPONG</span></h1>
        <p class="text-gray-300 text-lg">Jl. Gading Serpong Boulevard No. 3 Bez Plaza, Curug Banten, Tangerang 15810 Indonesia</p>
    </div>
</section>

<!-- Payment Content Section -->
<section class="full-width-section py-16 bg-gradient-to-b from-black to-gray-900">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Timer Alert - moved inside main content -->
        <div class="bg-[#4B4B4B] rounded-lg p-4 flex flex-col md:flex-row md:items-center md:justify-between gap-4 text-white mb-6">
            <!-- Kiri: Info -->
            <div class="flex items-start gap-3">
                <div class="w-8 h-8 bg-kreen-gold bg-opacity-20 rounded-full flex items-center justify-center mt-1">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="text-sm">
                    <p class="font-medium leading-snug text-white">We are holding your ticket,<br class="md:hidden"> please finish your payment before</p>
                    <p class="font-bold mt-1 text-kreen-gold ">16 Apr 2025, 10:11 WIB</p>
                </div>
            </div>

            <!-- Kanan: Timer -->
            <div class="flex justify-center md:justify-end items-center gap-2">
                <div class="bg-kreen-gold bg-opacity-20 rounded px-2 py-1 min-w-[44px] text-center">
                    <span class="text-white font-bold text-lg" id="hours">12</span>
                </div>
                <div class="bg-kreen-gold  bg-opacity-20 rounded px-2 py-1 min-w-[44px] text-center">
                    <span class="text-white font-bold text-lg" id="minutes">36</span>
                </div>
                <div class="bg-kreen-gold  bg-opacity-20 rounded px-2 py-1 min-w-[44px] text-center">
                    <span class="text-white font-bold text-lg" id="seconds">01</span>
                </div>
            </div>
        </div>
  
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-12 booking-grid">
            <!-- Left Side - Payment Form (8 columns) -->
            <div class="lg:col-span-8 space-y-8">
                <!-- Complete Payment -->
                <div>
                    <h2 class="text-white text-2xl font-bold mb-6">Complete Payment</h2>
                    
                    <!-- Bank Info -->
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-12 h-8 bg-green-600 rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">BJB</span>
                        </div>
                        <span class="text-white font-medium text-lg">Bank BJB</span>
                    </div>

                    <!-- Virtual Account Number -->
                    <div class="mb-6">
                        <label class="block text-white text-sm font-medium mb-2">Virtual Account Number</label>
                        <div class="flex items-center space-x-3">
                            <div class="flex-1 bg-gray-800 border border-gray-600 rounded-lg px-4 py-3">
                                <span class="text-white font-mono text-lg">***************</span>
                            </div>
                            <button class="copy-btn px-4 py-3 border border-gray-600 rounded-lg text-gray-400 hover:text-white transition-colors" onclick="copyToClipboard('***************')">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Total Payment -->
                    <div class="mb-6">
                        <label class="block text-white text-sm font-medium mb-2">Total Payment (IDR)</label>
                        <div class="flex items-center space-x-3">
                            <div class="flex-1 bg-gray-800 border border-gray-600 rounded-lg px-4 py-3">
                                <span class="text-white font-bold text-xl">Rp 154.000</span>
                            </div>
                            <button class="copy-btn px-4 py-3 border border-gray-600 rounded-lg text-gray-400 hover:text-white transition-colors" onclick="copyToClipboard('154000')">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Booking Code -->
                    <div class="mb-8">
                        <label class="block text-white text-sm font-medium mb-2">Booking Code</label>
                        <div class="bg-gray-800 border border-gray-600 rounded-lg px-4 py-3">
                            <span class="text-white font-mono text-sm break-all">INV/20243846283350634/384728374934w32SHBW</span>
                        </div>
                    </div>

                    <!-- Check Payment Status Button -->
                    <button class="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-bold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 text-lg">
                        CHECK PAYMENT STATUS
                    </button>
                </div>

                <!-- Payment Method -->
                <div>
                    <h2 class="text-white text-2xl font-bold mb-6">Payment Method</h2>
                    
                    <div class="space-y-4">
                        <!-- I-Banking -->
                        <div class="border border-gray-600 rounded-lg">
                            <button class="w-full flex items-center justify-between p-4 text-left hover:bg-gray-800 transition-colors" onclick="toggleDropdown('ibanking')">
                                <span class="text-white font-medium">Pembayaran Melalui I-Banking</span>
                                <svg class="w-5 h-5 text-gray-400 transform transition-transform" id="ibanking-arrow" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                            </button>
                            <div class="hidden border-t border-gray-600 p-4 bg-gray-800 bg-opacity-50" id="ibanking-content">
                                <ol class="text-gray-300 text-sm space-y-2 list-decimal list-inside">
                                    <li>Login ke aplikasi I-Banking Bank BJB</li>
                                    <li>Pilih menu "Transfer" atau "Pembayaran"</li>
                                    <li>Pilih "Virtual Account" atau "VA"</li>
                                    <li>Masukkan nomor Virtual Account: <span class="font-mono text-white">***************</span></li>
                                    <li>Masukkan nominal: <span class="font-bold text-white">Rp 154.000</span></li>
                                    <li>Konfirmasi pembayaran</li>
                                </ol>
                            </div>
                        </div>

                        <!-- Mobile Banking -->
                        <div class="border border-gray-600 rounded-lg">
                            <button class="w-full flex items-center justify-between p-4 text-left hover:bg-gray-800 transition-colors" onclick="toggleDropdown('mobile')">
                                <span class="text-white font-medium">Pembayaran Melalui Mobile Banking</span>
                                <svg class="w-5 h-5 text-gray-400 transform transition-transform" id="mobile-arrow" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                            </button>
                            <div class="hidden border-t border-gray-600 p-4 bg-gray-800 bg-opacity-50" id="mobile-content">
                                <ol class="text-gray-300 text-sm space-y-2 list-decimal list-inside">
                                    <li>Buka aplikasi Mobile Banking Bank BJB</li>
                                    <li>Login dengan PIN atau biometrik</li>
                                    <li>Pilih menu "Transfer" atau "Bayar"</li>
                                    <li>Pilih "Virtual Account"</li>
                                    <li>Masukkan nomor VA: <span class="font-mono text-white">***************</span></li>
                                    <li>Masukkan jumlah: <span class="font-bold text-white">Rp 154.000</span></li>
                                    <li>Konfirmasi dengan PIN</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Booking Summary (4 columns) -->
            <div class="lg:col-span-4">
                <div class="sticky-card border-2 border-kreen-gold rounded-xl p-6 space-y-6" style="background: linear-gradient(98.07deg, #000000 0%, #2E2E2E 25.48%, #000000 50.48%, #464646 81.73%, #040404 100%);">
                    <h3 class="text-white text-xl font-bold mb-4">Your Booking</h3>
                    
                    <!-- Venue -->
                    <div class="flex items-center space-x-3">
                        <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div>
                            <p class="text-white font-medium text-sm">HELEN'S NIGHT MART GADING SERPONG</p>
                        </div>
                    </div>

                    <!-- Date -->
                    <div class="flex items-center space-x-3">
                        <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div>
                            <p class="text-gray-400 text-sm">Tuesday, 17 June 2025</p>
                        </div>
                    </div>

                    <!-- Ticket -->
                    <div class="bg-white/20 bg-opacity-50 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 100 4v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2a2 2 0 100-4V6z"/>
                                </svg>
                            </div>
                            <div>
                                <p class="text-white text-sm font-medium">FDC Monday & Tuesday</p>
                                <p class="text-gray-400 text-xs">1 Ticket x Rp 150.000</p>
                            </div>
                        </div>
                    </div>

                    <!-- Divider -->
                    <hr class="border-gray-600">

                    <!-- Payment Details -->
                    <div class="space-y-3">
                        <h4 class="text-white font-semibold">Payment Detail</h4>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-400 text-sm">FDC Monday & Tuesday (x1)</span>
                            <span class="text-white text-sm">Rp 150.000</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-400 text-sm">Service Fee</span>
                            <span class="text-white text-sm">Rp 5.000</span>
                        </div>
                        
                        <hr class="border-gray-600">
                        
                        <div class="flex justify-between">
                            <span class="text-white font-medium">Total Payment</span>
                            <span class="text-kreen-gold font-bold text-lg">Rp 154.000</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    // Timer countdown
    function startTimer() {
        let hours = 12;
        let minutes = 36;
        let seconds = 1;
        
        const timer = setInterval(() => {
            seconds--;
            
            if (seconds < 0) {
                seconds = 59;
                minutes--;
                
                if (minutes < 0) {
                    minutes = 59;
                    hours--;
                    
                    if (hours < 0) {
                        clearInterval(timer);
                        // Timer expired
                        alert('Payment time expired!');
                        return;
                    }
                }
            }
            
            document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
            document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
        }, 1000);
    }
    
    startTimer();
    
    // Copy to clipboard function
    window.copyToClipboard = function(text) {
        navigator.clipboard.writeText(text).then(() => {
            // Show success message
            const originalText = event.target.closest('button').innerHTML;
            event.target.closest('button').innerHTML = '<span class="text-green-400">Copied!</span>';
            
            setTimeout(() => {
                event.target.closest('button').innerHTML = originalText;
            }, 2000);
        }).catch(() => {
            alert('Failed to copy to clipboard');
        });
    };
    
    // Toggle dropdown function
    window.toggleDropdown = function(id) {
        const content = document.getElementById(id + '-content');
        const arrow = document.getElementById(id + '-arrow');
        
        if (content.classList.contains('hidden')) {
            content.classList.remove('hidden');
            arrow.style.transform = 'rotate(180deg)';
        } else {
            content.classList.add('hidden');
            arrow.style.transform = 'rotate(0deg)';
        }
    };
});

// Header Navigation Replacement for Payment Flow (Step 2)
function replaceHeaderNavigation() {
    // Hide existing navigation
    const desktopNav = document.querySelector('nav.hidden.md\\:flex');
    const loginButton = document.querySelector('a[href=""].hidden.md\\:inline-block');
    
    if (desktopNav) {
        desktopNav.style.display = 'none';
    }
    if (loginButton) {
        loginButton.style.display = 'none';
    }
    
    // Create progress indicator with step 2 active
    const headerContainer = document.querySelector('header .flex.items-center.justify-between');
    if (headerContainer && !document.querySelector('.booking-progress')) {
        const progressContainer = document.createElement('div');
        progressContainer.className = 'booking-progress flex items-center space-x-8';
        progressContainer.innerHTML = `
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <span class="text-green-400 font-medium text-sm">Guest Info</span>
            </div>
            
            <div class="flex items-center">
                <div class="w-8 h-0.5 bg-kreen-gold"></div>
                <svg class="w-3 h-3 text-kreen-gold mx-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
            </div>
            
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-kreen-gold rounded-full flex items-center justify-center">
                    <span class="text-black text-sm font-bold">2</span>
                </div>
                <span class="text-kreen-gold font-medium text-sm">Payment</span>
            </div>
            
            <div class="flex items-center">
                <div class="w-8 h-0.5 bg-gray-600"></div>
                <svg class="w-3 h-3 text-gray-600 mx-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
            </div>
            
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 border-2 border-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-gray-600 text-sm font-bold">3</span>
                </div>
                <span class="text-gray-600 font-medium text-sm">You're In!</span>
            </div>
        `;
        
        // Insert progress indicator after logo
        const logoContainer = headerContainer.querySelector('.flex.items-center');
        if (logoContainer && logoContainer.nextElementSibling) {
            headerContainer.insertBefore(progressContainer, logoContainer.nextElementSibling);
        }
    }
}

// Initialize header replacement when page loads
replaceHeaderNavigation();
setTimeout(replaceHeaderNavigation, 100);

// Add mobile progress indicator
function addMobileProgress() {
    const mobileMenu = document.querySelector('#mobile-menu');
    if (mobileMenu && !document.querySelector('.booking-progress-mobile')) {
        const mobileProgress = document.createElement('div');
        mobileProgress.className = 'booking-progress-mobile md:hidden flex items-center justify-center space-x-4 px-4 py-3 bg-kreen-dark border-t border-gray-800';
        mobileProgress.innerHTML = `
            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <span class="text-green-400 font-medium text-[10px] md:text-xs">Info</span>
            </div>
            
            <svg class="w-3 h-3 text-kreen-gold" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            
            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 bg-kreen-gold rounded-full flex items-center justify-center">
                    <span class="text-black text-xs font-bold">2</span>
                </div>
                <span class="text-kreen-gold font-medium text-[10px] md:text-xs">Payment</span>
            </div>
            
            <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            
            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 border border-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-gray-600 text-xs font-bold">3</span>
                </div>
                <span class="text-gray-600 font-medium text-[10px] md:text-xs">In!</span>
            </div>
        `;
        
        mobileMenu.parentNode.insertBefore(mobileProgress, mobileMenu.nextSibling);
    }
}

addMobileProgress();
setTimeout(addMobileProgress, 100);
</script>
@endpush
