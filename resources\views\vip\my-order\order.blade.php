@extends('vip.app')

@section('title', 'My Order - KREEN VIP')

@push('styles')
<style>
/* Full width section */
.full-width-section {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    padding-left: calc(50vw - 50%);
    padding-right: calc(50vw - 50%);
}

/* Order card styling */
.order-card {
    background: linear-gradient(135deg, rgba(60, 60, 60, 0.9), rgba(80, 80, 80, 0.9));
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.order-image {
    width: 100px;
    height: 80px;
    border-radius: 0.75rem;
    object-fit: cover;
    flex-shrink: 0;
}

.order-details {
    flex: 1;
    color: white;
}

.order-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: white;
}

.order-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
    color: #D1D5DB;
    font-size: 0.875rem;
}

.order-info svg {
    width: 16px;
    height: 16px;
    color: #9CA3AF;
}

.view-button {
    background: #D4AF37;
    color: black;
    padding: 0.5rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.view-button:hover {
    background: #B8941F;
    transform: scale(1.05);
}

.order-again-btn {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: #000;
    font-weight: 600;
    padding: 12px 32px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.order-again-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(251, 191, 36, 0.3);
}

/* FIX: Proper centering for empty state */
.expired-container {
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.empty-state-content {
    text-align: center;
    max-width: 500px;
    margin: 0 auto;
    padding: 2rem;
}

.warning-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
}

.warning-icon img {
    width: 80px;
    height: 80px;
    object-fit: contain;
}

@media (max-width: 768px) {
    .order-card {
        flex-direction: column;
        text-align: center;
    }

    .order-image {
        width: 100%;
        height: 120px;
    }

    .empty-state-content {
        padding: 1rem;
        max-width: 90%;
    }

    .expired-title {
        font-size: 1.5rem !important;
        line-height: 1.3;
    }
}
</style>
@endpush

@section('content')
<!-- Container dengan Background Image -->
<div class="explore-nights-bg-container">
    <!-- My Order Section -->
    <section class="explore-nights-content py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

            <!-- Page Title -->
            <div class="text-start mb-8">
                <h1 class="text-white text-4xl font-bold mb-4">My Order</h1>
            </div>

            <!-- Orders List -->
            <div class="space-y-6">
            @forelse($orders as $order)
                <div class="order-card">
                    <img src="{{ $order->venue_banner ?? 'https://via.placeholder.com/300x200' }}" alt="{{ $order->venue_name }}" class="order-image">
                    <div class="order-details">

                    @if ($order->status != 404)
                        <span class="inline-block text-xs text-white font-semibold px-2 py-1 rounded mb-2 {{ $order->badge_color }}">
                            {{ $order->badge_label }}
                        </span>
                    @endif

                        <h3 class="order-title">{{ $order->venue_name }}</h3>
                        <div class="order-info">
                            <svg fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                            </svg>
                            <span>{{ \Carbon\Carbon::parse($order->date)->format('l, d F Y') }}</span>
                        </div>
                        <div class="order-info">
                            <svg fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 100 4v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2a2 2 0 100-4V6z"/>
                            </svg>
                            <span>{{ $order->total_tickets }} ticket{{ $order->total_tickets > 1 ? 's' : '' }}</span>
                        </div>
                    </div>
                    <a href="{{ route('order.myOrderDetail', ['order_id' => $order->id]) }}" class="view-button">View</a>
                </div>
            @empty
            <section class="full-width-section py-2">
                <div class="expired-container flex justify-center items-center min-h-[50vh]">
                    <div class="empty-state-content text-center">
                        <!-- Warning Icon -->
                        <div class="warning-icon flex justify-center mb-4">
                            <img src="{{ asset('image/data/expired.png') }}" alt="No Orders">
                        </div>

                        <!-- Title (1 line only) -->
                        <h1 class="expired-title text-2xl md:text-3xl font-bold text-white mb-4 whitespace-nowrap">
                            You haven't placed any orders yet.
                        </h1>

                        <!-- Description (1 line only) -->
                        <p class="text-gray-300 text-lg whitespace-nowrap mb-6">
                            Start exploring our events and make your first order!
                        </p>

                        <!-- Order Again Button -->
                        <button class="order-again-btn" onclick="orderAgain()">
                            Order Again
                        </button>
                    </div>
                </div>
            </section>

            @endforelse
        </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
function viewOrder(orderId) {
    // Redirect to order detail page or show modal
    alert('Viewing order: ' + orderId);
    // You can implement navigation to order detail page here
    // window.location.href = '/order-detail/' + orderId;
}

function orderAgain() {
    window.location.href = @json(route('venue.exploreNights'));
}
</script>
@endpush
