@extends('vip.app')

@section('title', __('Berhasil Mengirim Email'))

@section('content')
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br via-gray-900 to-kreen-dark">
    <div class="max-w-md w-full space-y-8">
        <div class="kreen-card kreen-golden-border p-8 backdrop-blur-sm">
            <!-- Logo -->
            <div class="mb-8">
                <div class="flex items-center justify-left mb-4">
                    <div class="w-[120px] h-[60px]">
                        <img src="{{ asset('image/kreenvip/kreenvip.png') }}" alt="KREEN VIP" class="h-12 w-auto object-contain" />
                    </div>
                </div>
                <h2 class="text-xl font-semibold text-white text-left">
                    {{ __('Berhasil Mengirim Email') }}
                </h2>
            </div>

            <!-- Gradient line -->
            <div class="mt-1 w-full h-[1px] bg-gradient-to-r from-[#A37A1D] via-[#FFEC95] to-[#281A00]"></div>

            <!-- Informasi -->
            <div class="mt-6 text-white text-sm leading-relaxed text-left">
                {{ __('Kami telah mengirimkan email berisi tautan untuk mengatur ulang password Anda. Silakan periksa kotak masuk atau folder spam pada email Anda.') }}
            </div>

            <!-- Tombol kembali -->
            <div class="mt-6">
                <a href="{{ route('auth.login') }}"
                   class="w-full inline-block bg-kreen-gold hover:bg-kreen-gold-hover text-black font-semibold py-3 px-6 rounded-lg text-center transition-all duration-200 transform hover:scale-105">
                    {{ __('Kembali') }}
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
