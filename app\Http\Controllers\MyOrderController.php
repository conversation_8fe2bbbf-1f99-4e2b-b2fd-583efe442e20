<?php

namespace App\Http\Controllers;

use App\Helpers\ApiHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class MyOrderController extends Controller
{

    public function index()
    {    
        $id_user = Auth::id();
    
        $orders = DB::table('tbl_bar_order as o')
            ->join('tbl_bar_venue as v', DB::raw('CONVERT(o.id_venue USING utf8mb4)'), '=', DB::raw('CONVERT(v.id USING utf8mb4)'))
            ->where('o.id_user', Auth::id())
            ->select(
                'o.*',
                'v.nama as venue_name',
                'v.banner as venue_banner',
                'v.alamat as venue_address'
            )
            ->orderByDesc('o.created_at')
            ->get();

            // dd($orders);

            // Ambil semua order_id
            $orderIds = $orders->pluck('id');

            // Ambil total tiket per order_id dari order_detail
            $orderDetails = DB::table('tbl_bar_order_detail')
                ->select('id_order', DB::raw('SUM(qty) as total_qty'))
                ->whereIn('id_order', $orderIds)
                ->groupBy('id_order')
                ->get()
                ->keyBy('id_order');

            // Tambahkan total qty ke masing-masing order
            foreach ($orders as $order) {
                $order->total_tickets = $orderDetails[$order->id]->total_qty ?? 0;
                $badge = $this->getOrderStatusBadge($order->status);
                $order->badge_label = $badge['label'];
                $order->badge_color = $badge['color'];
            }

        $totalTickets = $orderDetails->sum('total_qty'); // total semua tiket dari semua order
        return view('vip.my-order.order', compact('orders', 'totalTickets'));
            
    }

    public function detail($orderId)
    {
        $userId = Auth::id();

        $order = DB::table('tbl_bar_order as o')
            ->join('tbl_bar_venue as v', DB::raw('CONVERT(o.id_venue USING utf8mb4)'), '=', DB::raw('CONVERT(v.id USING utf8mb4)'))
            ->where('o.id', $orderId)
            ->where('o.id_user', $userId)
            ->select(
                'o.*',
                'v.nama as venue_name',
                'v.banner as venue_banner',
                'v.alamat as venue_address'
            )
            ->first();

        if (!$order) {
            abort(404, 'Order tidak ditemukan');
        }

        $orderDetails = DB::table('tbl_bar_order_detail')
            ->where('id_order', $orderId)
            ->get();

        $totalTickets = $orderDetails->sum('qty');
        $badge = $this->getOrderStatusBadge($order->status);
        $order->badge_label = $badge['label'];
        $order->badge_color = $badge['color'];
        
        return view('vip.my-order.detail', compact('order', 'orderDetails','totalTickets'));
    }

    private function getOrderStatusBadge($status)
    {
        $statusMap = [
            0 => ['label' => 'Failed', 'color' => 'bg-red-500'],
            1 => ['label' => 'Completed', 'color' => 'bg-green-500'],
            2 => ['label' => 'Canceled', 'color' => 'bg-gray-500'],
            3 => ['label' => 'Waiting', 'color' => 'bg-yellow-500'],
            4 => ['label' => 'Processing', 'color' => 'bg-blue-500'],
            20 => ['label' => 'Expired', 'color' => 'bg-red-400'],
            404 => ['label' => 'Hidden', 'color' => 'bg-gray-400'],
        ];

        return $statusMap[$status] ?? ['label' => 'Unknown', 'color' => 'bg-gray-500'];
    }



    // public function detail()
    // {
    //     return view('vip.my-order.detail');
    // }


}
