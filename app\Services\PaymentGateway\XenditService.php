<?php

namespace App\Services\PaymentGateway;

use Exception;
use Carbon\Carbon;
use App\Helpers\Format;
use App\Helpers\ApiHelper;
use Illuminate\Support\Str;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Services\PaymentGateway\PaymentGatewayInterface;

class XenditService implements PaymentGatewayInterface
{

    public function __construct(protected WebhookHandler $webhookHandler)
    {
    }


    private function getAuthToken($country): string
    {
        $format = env('XENDIT_SECRET_KEY_' . $country) . ':';
        $base64 = base64_encode($format);
        return $base64;
    }

    private function getHeadersRequestPayment($country): array
    {
        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Basic ' . $this->getAuthToken($country),
        ];
        return $headers;
    }

    public function simulatePayment(Request $request): JsonResponse
    {
        $payment_request_id = $request->input('payment_request_id');
        $amount = $request->input('amount');
        $endpoint = "/v3/payment_requests/{$payment_request_id}/simulate";
        $country = 'ID';
        $url = env('XENDIT_BASE_URL') . $endpoint;
        $headers = $this->getHeadersRequestPayment($country);
        $headers = array_merge($headers, [
            'api-version' => "2024-11-11",
        ]);
        try {
            $response = Http::withHeaders($headers)->post($url, [
                'amount' => (int)$amount,
            ]);
            $dataXendit = $response->json();
            Log::info('xendit Simulate QRIS Response: ', ['response' => $dataXendit]);
            if ($response->status() !== 200) {
                return ApiHelper::errorRes2([
                    'message' => $dataXendit['message'] ?? $dataXendit,
                    'rc' => $response->status(),
                    'data' => $dataXendit,
                ]);
            } else {
                return ApiHelper::successRes2([
                    'message' => 'Simulate QRIS Success',
                    'data' => $dataXendit,
                ]);
            }
        } catch (Exception $e) {
            DB::rollBack();
            $error = [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payload' => $request->all(),
                'rc' => 500,
            ];
            Log::info('Internal Server Error Xendit Simulate Transaction: ', $error);
            return ApiHelper::errorRes2($error);
        }
    }

    public function createTransaction(array $payload): array|JsonResponse
    {
        $endpoint = "/payment_requests";
        $url = env('XENDIT_BASE_URL') . $endpoint;
        $function_name = "{$payload['payment_method_code_category']}Body";
        $id_ref = 'vip-' . Str::random(16);
        $body = $this->$function_name([
            'id_order' => $payload['id'],
            'id_ref' => $id_ref,
            'total_payment' => $payload['total_price'],
            'fees' => $payload['fees'],
            'full_name' => $payload['full_name'],
            'phone' => $payload['phone'],
            'email' => $payload['email'],
            'bank_code' => $payload['bank_code'],
            'mobile_number' => $payload['mobile_number'],
            'currency' => $payload['currency'],
            'currency_pg' => $payload['currency_pg'] ?? "IDR",
            'total_amount' => $payload['total_amount'],
            'slug' => $payload['slug'],
            'country' => $payload['country'],
            'card_number' => $payload['card_number'],
            'id_card' => $payload['id_card'],
            'expiry_month' => $payload['expiry_month'],
            'expiry_year' => $payload['expiry_year'],
            'cvv' => $payload['cvv'],
            'type_product' => 'bar',
            'payment_method' => $payload['payment_method'],
            'reusability' => $payload['reusability'] ?? 'ONE_TIME_USE',
        ]);
        $headers = $this->getHeadersRequestPayment($payload['country']);
        try {
            $response = Http::withHeaders($headers)->post($url, $body);
            $dataXendit = $response->json();
            Log::info('xendit Create QRIS Response: ', ['response' => $dataXendit]);
            if ($response->status() !== 201) {
                return ApiHelper::errorRes2([
                    'message' => $dataXendit['message'] ?? $dataXendit,
                    'rc' => $response->status(),
                    'payload' => $body,
                    'data' => $dataXendit,
                ]);
            } else {
                return $dataXendit;
            }
        } catch (Exception $e) {
            DB::rollBack();
            $error = [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payload' => $body,
                'rc' => 500,
            ];
            Log::info('Internal Server Error Xendit Create Transaction: ', $error);
            return ApiHelper::errorRes2($error);
        }

    }

    // public function getTransaction(string $externalId): array
    // {
    //     return \Xendit\Invoice::retrieve($externalId);
    // }

    public function handleCallback(string $event, array $data): JsonResponse
    {
        // Split Event
        try {
            try {
                $splitEvent = explode('.', $event);
                $typeEvent = $splitEvent[0];
                $statusEvent = $splitEvent[1];
            } catch (Exception $e) {
                return ApiHelper::errorRes2([
                    'message' => 'Event xendit tidak dikenali',
                    'rc' => 400,
                ]);
            }

            // Get Transaction
            $query = DB::table('tbl_pgxd_payment_method_request as mr');

            if ($typeEvent === 'payment' || $typeEvent === 'capture') {
                if (isset($data['payment_method']['id'])) {
                    $query->where('mr.payment_method_id', $data['payment_method']['id']);
                }

                if (isset($data['payment_request_id'])) {
                    $query->where('mr.id', $data['payment_request_id']);
                }

                if (isset($data['payment_method']['reference_id'])) {
                    $query->where('mr.reference_id', $data['payment_method']['reference_id']);
                }
            }

            if ($typeEvent === 'payment_method') {
                if (isset($data['id'])) {
                    $query->where('mr.payment_method_id', $data['id']);
                }

                if (isset($data['reference_id'])) {
                    $query->where('mr.reference_id', $data['reference_id']);
                }
            }

            $transaction = $query->limit(1)->first();
            if (!$transaction) {
                return ApiHelper::errorRes2([
                    'message' => 'Transaction not found',
                    'rc' => 404,
                ]);
            }

            $result = $this->webhookHandler->handler('tbl_pgxd_payment_method_request', $transaction->type_order, $transaction->order_id, $statusEvent);

            if (!$result['success']) {
                Log::error("Xendit callback error: ", ['result' => $result]);
                $response = [
                    'message' => $result['message'] ?? 'Gagal update status order',
                    'rc' => 400,
                ];
                return ApiHelper::errorRes2($response);
            }
            // Return Success
            return ApiHelper::successRes2([
                'message' => 'Sukses update status order',
            ]);
        } catch (Exception $e) {
            $error = [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'rc' => 500,
            ];
            Log::info('Internal Server Error Xendit Handle Callback: ', $error);
            return ApiHelper::errorRes2($error);
        }
    }

    private function generateRequestBody($params = [])
    {
        $total_amount = $params['total_amount'] ?? 0;
        $currency_pg = $params['currency_pg'] ?? 'IDR';
        $country = $params['country'] ?? 'ID';

        $body = [
            "currency" => $currency_pg,
            "amount" => $total_amount,
            "country" => $country,
            "payment_method" => $params['pm_body'],
            "customer_id" => $params['customer_id'] ?? null,
        ];

        if (isset($params["body"]) && is_array($params["body"])) {
            $body = array_merge($body, $params["body"]);
        }

        return $body;
    }

    private function paymentMethodBody($params = [])
    {
        $body = [
            "type" => $params['type_pm'],
            "reusability" => $params['reusability'] ?? 'ONE_TIME_USE',
            "ewallet" => $params['ewallet'] ?? null,
            "direct_debit" => $params['direct_debit'] ?? null,
            "virtual_account" => $params['virtual_account'] ?? null,
            "over_the_counter" => $params['over_the_counter'] ?? null,
            "card" => $params['card'] ?? null,
            "qr_code" => $params['qr_code'] ?? null,
        ];

        return $body;
    }

    private function vaBody($params = [])
    {
        $params['pm_body'] ??= [];
        $type_pm = "VIRTUAL_ACCOUNT";
        $expired_duration = $params['payment_method']->expired_duration;
        $virtual_account_body = [
            'channel_code' => $params['bank_code'],
            'currency' => $params['currency'],
            'amount' => $params['total_amount'],
            'channel_properties' => [
                'customer_name' => $params['full_name'],
                'expires_at' => Carbon::now()->addSeconds($expired_duration)->setTimezone('UTC')->toIso8601String(),
            ],
        ];

        $params['pm_body'] = $this->paymentMethodBody([
            'type_pm' => $type_pm,
            'virtual_account' => $virtual_account_body,
        ]);

        return $this->generateRequestBody($params);
    }

    private function qrcodeBody($params = [])
    {
        $params['pm_body'] ??= [];
        $type_pm = "QR_CODE";
        $expired_duration = $params['payment_method']->expired_duration;

        $qr_code_body = [
            'channel_code' => $params['country'] === "TH" ? "PROMPTPAY" : null,
            'currency' => $params['currency'],
            'amount' => $params['total_amount'],
            'channel_properties' => [
                'expires_at' => Carbon::now()->addSeconds($expired_duration)->setTimezone('UTC')->toIso8601String(),
            ],
        ];

        $params['pm_body'] = $this->paymentMethodBody([
            'type_pm' => $type_pm,
            'qr_code' => $qr_code_body,
        ]);

        return $this->generateRequestBody($params);
    }

    private function ewalletBody($params = [])
    {
        $params['pm_body'] ??= [];
        $type_pm = "EWALLET";

        $successRoute = fn() => route('order.successPayment', ['id_order' => $params['id_order']]);
        $failRoute = fn() => route('order.failedPayment', ['id_order' => $params['id_order']]);

        $ewallet_body = [
            'channel_code' => $params['bank_code'],
            'channel_properties' => [],
        ];

        $bank_code = strtolower($params['bank_code']);
        $channel_properties = [];

        if ($bank_code === 'ovo' && $params['reusability'] === "ONE_TIME_USE") {
            $channel_properties['mobile_number'] = Format::formatPhone($params['mobile_number'], $params['country']);
        }

        $withSuccessUrl = ["dana", "linkaja", "shopeepay", "gcash", "astrapay", "wechatpay", "linepay", "truemoney", "touchngo", "grabpay", "paymaya", "jeniuspay", "appota", "momo", "zalopay", "vnptwallet", "viettelpay"];
        if (in_array($bank_code, $withSuccessUrl)) {
            $channel_properties['success_return_url'] = $successRoute();
        }

        $withPendingUrl = ["appota", "momo", "zalopay", "vnptwallet", "viettelpay"];
        if (in_array($bank_code, $withPendingUrl)) {
            $channel_properties['pending_return_url'] = $failRoute();
        }

        $withFailureUrl = ["grabpay", "ovo", "dana", "linkaja", "shopeepay", "astrapay", "gcash", "touchngo"];
        if (in_array($bank_code, $withFailureUrl) && $params['reusability'] === "MULTIPLE_USE") {
            $channel_properties['failure_return_url'] = $failRoute();
        }

        if ($bank_code === 'astrapay') {
            $channel_properties['failure_return_url'] = $failRoute();
        }

        switch ($bank_code) {
            case 'grabpay':
                $grabpayRegions = ["PH", "MY"];
                if (in_array($params['country'], $grabpayRegions)) {
                    $channel_properties['failure_return_url'] = $failRoute();
                    if ($params['country'] === "MY") {
                        $channel_properties['allowed_payment_options'] = ["ONE_TIME_USE"];
                    }
                }
                break;

            case 'gcash':
                $channel_properties['failure_return_url'] = $failRoute();
                break;

            case 'shopeepay':
                $shopeepayRegions = ["VN"];
                if (in_array($params['country'], $shopeepayRegions)) {
                    $channel_properties['pending_return_url'] = $failRoute();
                }
                break;

            case 'paymaya':
                $channel_properties['failure_return_url'] = $failRoute();
                $channel_properties['cancel_return_url'] = $failRoute();
                break;

            case 'jeniuspay':
                $channel_properties['cashtag'] = '#vote';
                break;
        }

        $ewallet_body['channel_properties'] = $channel_properties;

        $params['pm_body'] = $this->paymentMethodBody([
            'type_pm' => $type_pm,
            'ewallet' => $ewallet_body,
        ]);

        return $this->generateRequestBody($params);
    }


    private function directdebitBody($params = [])
    {
        $params['pm_body'] ??= [];
        $type_pm = "DIRECT_DEBIT";

        $successRoute = fn() => route('order.successPayment', ['id_order' => $params['id_order']]);
        $failRoute = fn() => route('order.failedPayment', ['id_order' => $params['id_order']]);

        $direct_debit_body = [
            'channel_code' => $params['bank_code'],
            'channel_properties' => []
        ];

        $bank_code = strtolower($params['bank_code']);
        $channel_properties = [];

        if ($bank_code === "bri") {
            $channel_properties['card_last_four'] = substr($params['card_number'], -4) ?? null;
            $channel_properties['card_expiry'] = $params['expiry_month'] . '/' . $params['expiry_year'] ?? null;
            $channel_properties['email'] = $params['email'] ?? null;
        }

        $withUrl = ["mandiri", "bpi", "ubp", "rcbc", "chinabank", "scb", "bbl", "ktb", "bay"];
        if (in_array($bank_code, $withUrl) || strpos($bank_code, 'fpx') !== false) {
            $channel_properties['success_return_url'] = $successRoute();
            $channel_properties['failure_return_url'] = $failRoute();
        }

        $banksWithMobileNumber = ['bri', 'scb', 'bbl', 'ktb', 'bay'];

        if (in_array($bank_code, $banksWithMobileNumber)) {
            $channel_properties['mobile_number'] = Format::formatPhone($params['mobile_number'], $params['country']);
        }

        if (in_array($bank_code, ['ktb', 'bay'])) {
            $channel_properties['identity_document_number'] = $params['id_card'];
        }

        $direct_debit_body['channel_properties'] = $channel_properties;

        $customer = $this->createCustomer($params);
        if ($customer) {
            $params['customer_id'] = $customer['id'];
        }

        $params['pm_body'] = $this->paymentMethodBody([
            'type_pm' => $type_pm,
            'direct_debit' => $direct_debit_body,
        ]);

        return $this->generateRequestBody($params);
    }

    private function retailBody($params = [])
    {
        $params['pm_body'] ??= [];
        $type_pm = "OVER_THE_COUNTER";

        $over_the_counter_body = [
            'channel_code' => $params['bank_code'],
            'currency' => $params['currency'],
            'amount' => $params['total_amount'],
            'channel_properties' => [
                'customer_name' => $params['full_name'],
                'expires_at' => Carbon::now()->addSeconds(2 * 60 * 60)->setTimezone('UTC')->toIso8601String(),
            ]
        ];

        $params['pm_body'] = $this->paymentMethodBody([
            'type_pm' => $type_pm,
            'over_the_counter' => $over_the_counter_body,
        ]);

        return $this->generateRequestBody($params);
    }

    private function creditcardBody($params = [])
    {
        $params['pm_body'] ??= [];
        $type_pm = "CARD";

        $successRoute = fn() => route('order.successPayment', ['id_order' => $params['id_order']]);
        $failRoute = fn() => route('order.failedPayment', ['id_order' => $params['id_order']]);

        $card_body = [
            'currency' => $params['currency'],
            'channel_properties' => [
                'skip_three_d_secure' => false,
                'cardonfile_type' => 'CUSTOMER_UNSCHEDULED',
                'success_return_url' => $successRoute(),
                'failure_return_url' => $failRoute(),
            ],
            'card_information' => [
                'card_number' => $params['card_number'],
                'expiry_month' => $params['expiry_month'],
                'expiry_year' => strlen($params['expiry_year']) == 2 ? '20' . $params['expiry_year'] : $params['expiry_year'],
                'cardholder_name' => $params['cardholder_name'] ?? $params['full_name'],
                'cardholder_email' => $params['cardholder_email'] ?? null,
                'cardholder_phone_number' => $params['cardholder_phone_number'] ?? null,
            ],
        ];

        $params['pm_body'] = $this->paymentMethodBody([
            'type_pm' => $type_pm,
            'card' => $card_body,
        ]);

        return $this->generateRequestBody($params);
    }

    private function createCustomer($params = [])
    {
        $bank_code = $params['bank_code'];
        $customer_body = [
            'reference_id' => Str::uuid()->toString(),
        ];
        if (strpos($bank_code, 'business') !== false) {
            $customer_body['type'] = 'BUSINESS';
            $customer_body['business_detail'] = [
                'business_name' => env('APP_NAME'),
                'business_type' => 'CORPORATION',
            ];
        } else {
            $customer_body['type'] = 'INDIVIDUAL';
            $customer_body['individual_detail'] = [
                'given_names' => $params['full_name'],
            ];
        }
        $url = env("XENDIT_BASE_URL") . '/customers';
        $headers = $this->getHeadersRequestPayment($params['country']);
        $headers['API-VERSION'] = '2020-10-31';

        try {
            $dataXendit = Http::withHeaders($headers)->post($url, $customer_body)->json();
            Log::info('createCustomer Xendit', ['response' => $dataXendit, 'body' => $customer_body, 'headers' => $headers, 'url' => $url]);
            if (empty($dataXendit)) {
                Log::error('Error in createCustomer: ' . 'response is empty');
                return null;
            }
        } catch (Exception $e) {
            Log::error('Error in createCustomer: ' . $e->getMessage());
            return null;
        }
        return $dataXendit;
    }
}
