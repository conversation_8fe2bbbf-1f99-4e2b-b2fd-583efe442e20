<?php

namespace App\Http\Controllers;

use App\Helpers\ApiHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class HomeController extends Controller
{
    public function index()
    {
        $banners = DB::table('tbl_bar_banner')->select('id', 'urutan', 'gambar', 'header', 'narasi', 'link')->where('flag_aktif', '1')->where('published_at', '<=', date('Y-m-d H:i:s'))->where(function ($query) {
            $query->where('expired_at', '>=', date('Y-m-d H:i:s'))->orWhereNull('expired_at');
        })->orderBy('urutan', 'asc')->get();

        $djs = DB::table('tbl_bar_dj')->select('id', 'nama', 'gambar', 'slug')->where('flag_aktif', '1')->orderBy('created_at', 'desc')->get();

        // Ambil hanya kota yang dipakai (distinct)
        $venue_cities = DB::select("SELECT DISTINCT v.id_kota as id, ar.name
                            FROM tbl_bar_venue as v
                            JOIN kreen_production_online.tbl_adm_regency as ar ON v.id_kota  = ar.id
                            WHERE v.flag_aktif = '1'
                            ORDER BY ar.name");

        $event_cities = DB::select("SELECT DISTINCT e.id_kota as id, ar.name
                            FROM tbl_bar_event as e
                            JOIN kreen_production_online.tbl_adm_regency as ar ON e.id_kota  = ar.id
                            WHERE e.flag_aktif = '1' AND e.tanggal >= DATE(NOW())
                            ORDER BY ar.name");

        return view('vip.master.index', compact('banners', 'djs', 'venue_cities', 'event_cities'));
    }

    public function ajaxExploreNights(Request $request)
    {
        $limit = $request->input('limit', 12);
        $page = $request->input('page', 1);
        $offset = ($page - 1) * $limit;
        $search = $request->input('search');
        $id_kota = $request->input('id_kota');

        // Base query
        $baseQuery = DB::table('tbl_bar_venue as v')
            ->join(
                'kreen_production_online.tbl_adm_regency as ar',
                DB::raw("v.id_kota "),
                '=',
                DB::raw("ar.id ")
            )
            ->where('v.flag_aktif', '1');

        if ($search) {
            $baseQuery->where('v.nama', 'like', "%{$search}%");
        }

        if ($id_kota) {
            $baseQuery->where('v.id_kota', $id_kota);
        }

        // Clone for count
        $count_all = (clone $baseQuery)->count();
        $has_more = $count_all > $page * $limit;

        // Ambil data paginated
        $data = (clone $baseQuery)
            ->select('v.id', 'v.nama', 'ar.name as nama_kota', 'v.slug', 'v.banner')
            ->orderBy('v.nama', 'asc')
            ->offset($offset)
            ->limit($limit)
            ->get();

        $last_page = ceil($count_all / $limit);

        $view = view('vip.master.ajax.venues', [
            'venues' => $data,
            'pagination' => [
                'total_page' => $last_page,
                'total_item' => $count_all,
                'page' => $page,
                'limit' => $limit,
            ],
        ])->render();

        return response()->json([
            'view' => $view,
            'count' => $count_all,
            'has_more' => $has_more,
        ]);
    }

    public function ajaxExploreEvents(Request $request)
    {
        $limit = $request->input('limit', 12);
        $page = $request->input('page', 1);
        $offset = ($page - 1) * $limit;
        $search = $request->input('search');
        $id_kota = $request->input('id_kota');

        // Base query
        $baseQuery = DB::table('tbl_bar_event as e')
            ->join(
                'kreen_production_online.tbl_adm_regency as ar',
                DB::raw("e.id_kota "),
                '=',
                DB::raw("ar.id ")
            )
            ->where('e.flag_aktif', '5')
            ->where('e.tanggal', '>=', date('Y-m-d'));

        if ($search) {
            $baseQuery->where('e.nama', 'like', "%{$search}%");
        }

        if ($id_kota) {
            $baseQuery->where('e.id_kota', $id_kota);
        }

        // Clone for count
        $count_all = (clone $baseQuery)->count();
        $has_more = $count_all > $page * $limit;

        // Ambil data paginated
        $data = (clone $baseQuery)
            ->select('e.id', 'e.nama', 'e.tanggal', 'ar.name as nama_kota', 'e.slug', 'e.gambar')
            ->orderBy('e.tanggal', 'asc')
            ->offset($offset)
            ->limit($limit)
            ->get();

        $last_page = ceil($count_all / $limit);

        $view = view('vip.master.ajax.events', [
            'events' => $data,
            'pagination' => [
                'total_page' => $last_page,
                'total_item' => $count_all,
                'page' => $page,
                'limit' => $limit,
            ],
        ])->render();

        return response()->json([
            'view' => $view,
            'count' => $count_all,
            'has_more' => $has_more,
        ]);
    }

    public function payments()
    {
        return view('vip.master.payments');
    }

    public function ticket()
    {
        return view('vip.master.ticket');
    }

    public function dj_detail()
    {
        return view('vip.master.our_dj');
    }

    public function dj_profile()
    {
        return view('vip.master.profile');
    }
}
