@extends('layouts.admin')

@section('title', 'Booking List - Admin Panel')
@section('page-title', 'Booking List')

@section('content')
<div class="space-y-4 sm:space-y-6">
    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-3 sm:gap-4">
        <button class="flex items-center justify-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium text-sm">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15.586 13H14a1 1 0 01-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Check-out Scanner</span>
        </button>
        <button class="flex items-center justify-center space-x-2 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-medium text-sm">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15.586 13H14a1 1 0 01-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Check-in Scanner</span>
        </button>
        <button class="flex items-center justify-center space-x-2 px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors font-medium text-sm">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15.586 13H14a1 1 0 01-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Scanner</span>
        </button>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 sm:p-6">
        <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <!-- Date Picker -->
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                </svg>
                <input type="date" id="date-filter" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
            </div>

            <!-- Venue Filter -->
            <div class="flex items-center space-x-2">
                <select id="venue-filter" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                    <option value="all">Semua Venue</option>
                    @foreach($venues as $venue)
                        <option value="{{ $venue->id }}" {{ $selectedVenue == $venue->id ? 'selected' : '' }}>{{ $venue->nama }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Limit Dropdown -->
            <div class="flex items-center space-x-2">
                <select id="limit-filter" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                    <option value="10" {{ $selectedLimit == '10' ? 'selected' : '' }}>10</option>
                    <option value="25" {{ $selectedLimit == '25' ? 'selected' : '' }}>25</option>
                    <option value="50" {{ $selectedLimit == '50' ? 'selected' : '' }}>50</option>
                    <option value="100" {{ $selectedLimit == '100' ? 'selected' : '' }}>100</option>
                </select>
            </div>

            <!-- Filter Button -->
            <button id="filter-btn" class="px-4 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50 transition-colors">
                Filter
                <svg class="w-4 h-4 inline ml-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>

            <!-- Search -->
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <input type="text" id="search-filter" placeholder="Search by name, phone, email, or invoice..." value="{{ $searchQuery }}" class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                    <svg class="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Table -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 border-b border-gray-200">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal Kunjungan</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nama Pengunjung</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">QR ID</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nomor WhatsApp</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status Tiket</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipe Tiket</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status Kehadiran</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
                    </tr>
                </thead>
                <tbody id="booking-table-body" class="bg-white divide-y divide-gray-200">
                    @forelse($bookings as $booking)
                    <tr class="hover:bg-gray-50">
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{{ $booking['tanggal_kunjungan'] }}</td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{{ $booking['nama_pengunjung'] }}</td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{{ $booking['qr_id'] }}</td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{{ $booking['nomor_whatsapp'] }}</td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            @if($booking['status_tiket_color'] == 'green')
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    {{ $booking['status_tiket'] }}
                                </span>
                            @elseif($booking['status_tiket_color'] == 'red')
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                    {{ $booking['status_tiket'] }}
                                </span>
                            @elseif($booking['status_tiket_color'] == 'yellow')
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    {{ $booking['status_tiket'] }}
                                </span>
                            @else
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                    {{ $booking['status_tiket'] }}
                                </span>
                            @endif
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{{ $booking['tipe_tiket'] }}</td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            @if($booking['status_kehadiran_color'] == 'blue')
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {{ $booking['status_kehadiran'] }}
                                </span>
                            @elseif($booking['status_kehadiran_color'] == 'red')
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                    {{ $booking['status_kehadiran'] }}
                                </span>
                            @elseif($booking['status_kehadiran_color'] == 'gray')
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                    {{ $booking['status_kehadiran'] }}
                                </span>
                            @else
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                    {{ $booking['status_kehadiran'] }}
                                </span>
                            @endif
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div class="flex space-x-2">
                                <button class="view-detail-btn text-blue-600 hover:text-blue-900"
                                        data-booking='@json($booking)'
                                        title="View Details">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                                <button class="text-green-600 hover:text-green-900" title="Mark as Checked In">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="px-4 py-12 text-center">
                            <div class="flex flex-col items-center justify-center space-y-3">
                                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <div class="text-gray-500">
                                    <p class="text-lg font-medium">No Booking List data available</p>
                                    <p class="text-sm">There are no bookings for the selected date and filters.</p>
                                </div>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination Info -->
    <div class="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
        <div id="pagination-info" class="text-sm text-gray-500">
            Showing {{ count($bookings) }} results
        </div>
    </div>
</div>

<!-- Modal Detail Booking -->
<div id="detail-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Change Status</h3>
                <button id="close-modal" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Tanggal Kunjungan</label>
                    <p id="modal-tanggal" class="text-sm text-gray-900 font-semibold">-</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Nama Pengunjung</label>
                    <p id="modal-nama" class="text-sm text-gray-900">-</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">QR ID</label>
                    <p id="modal-qr" class="text-sm text-gray-900">-</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Nomor WhatsApp</label>
                    <p id="modal-whatsapp" class="text-sm text-gray-900">-</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status Tiket</label>
                    <span id="modal-status-tiket" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">-</span>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Tipe Tiket</label>
                    <p id="modal-tipe" class="text-sm text-gray-900">-</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status Kehadiran</label>
                    <span id="modal-status-kehadiran" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">-</span>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Check-in Time</label>
                    <p id="modal-checkin" class="text-sm text-gray-900">-</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Check-Out Time</label>
                    <p id="modal-checkout" class="text-sm text-gray-900">-</p>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex justify-between mt-6 space-x-3">
                <button id="cancel-btn" class="px-4 py-2 border-2 border-yellow-500 text-yellow-600 rounded-lg hover:bg-yellow-50 transition-colors font-medium text-sm flex-1">
                    Cancel
                </button>
                <button id="checkin-btn" class="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors font-medium text-sm flex-1 flex items-center justify-center space-x-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Check-in</span>
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

<!-- Custom Alert Modal -->
<div id="custom-alert" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <!-- Alert Icon -->
            <div id="alert-icon" class="mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4">
                <!-- Success Icon -->
                <svg id="success-icon" class="h-6 w-6 text-green-600 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <!-- Error Icon -->
                <svg id="error-icon" class="h-6 w-6 text-red-600 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                <!-- Warning Icon -->
                <svg id="warning-icon" class="h-6 w-6 text-yellow-600 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <!-- Info Icon -->
                <svg id="info-icon" class="h-6 w-6 text-blue-600 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>

            <!-- Alert Title -->
            <h3 id="alert-title" class="text-lg leading-6 font-medium text-gray-900 mb-2">Alert</h3>

            <!-- Alert Message -->
            <div id="alert-message" class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">Alert message goes here</p>
            </div>

            <!-- Alert Buttons -->
            <div id="alert-buttons" class="items-center px-4 py-3">
                <button id="alert-ok-btn" class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300">
                    OK
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Custom Alert Functions
class CustomAlert {
    constructor() {
        this.modal = document.getElementById('custom-alert');
        this.title = document.getElementById('alert-title');
        this.message = document.getElementById('alert-message');
        this.iconContainer = document.getElementById('alert-icon');
        this.okBtn = document.getElementById('alert-ok-btn');

        // Icons
        this.successIcon = document.getElementById('success-icon');
        this.errorIcon = document.getElementById('error-icon');
        this.warningIcon = document.getElementById('warning-icon');
        this.infoIcon = document.getElementById('info-icon');

        this.setupEventListeners();
    }

    setupEventListeners() {
        this.okBtn.addEventListener('click', () => this.hide());

        // Close on backdrop click
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) this.hide();
        });

        // Close on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !this.modal.classList.contains('hidden')) {
                this.hide();
            }
        });
    }

    show(type, title, message) {
        this.setIcon(type);
        this.title.textContent = title;
        this.message.innerHTML = `<p class="text-sm text-gray-500">${message}</p>`;
        this.setButtonColor(type);
        this.modal.classList.remove('hidden');

        // Auto focus on OK button
        setTimeout(() => this.okBtn.focus(), 100);

        return new Promise((resolve) => {
            this.okBtn.onclick = () => {
                this.hide();
                resolve(true);
            };
        });
    }

    setIcon(type) {
        // Hide all icons
        this.successIcon.classList.add('hidden');
        this.errorIcon.classList.add('hidden');
        this.warningIcon.classList.add('hidden');
        this.infoIcon.classList.add('hidden');

        // Remove all background colors
        this.iconContainer.className = 'mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4';

        switch(type) {
            case 'success':
                this.successIcon.classList.remove('hidden');
                this.iconContainer.classList.add('bg-green-100');
                break;
            case 'error':
                this.errorIcon.classList.remove('hidden');
                this.iconContainer.classList.add('bg-red-100');
                break;
            case 'warning':
                this.warningIcon.classList.remove('hidden');
                this.iconContainer.classList.add('bg-yellow-100');
                break;
            case 'info':
            default:
                this.infoIcon.classList.remove('hidden');
                this.iconContainer.classList.add('bg-blue-100');
                break;
        }
    }

    setButtonColor(type) {
        // Remove all color classes
        this.okBtn.className = 'px-4 py-2 text-white text-base font-medium rounded-md w-full shadow-sm focus:outline-none focus:ring-2';

        switch(type) {
            case 'success':
                this.okBtn.classList.add('bg-green-500', 'hover:bg-green-700', 'focus:ring-green-300');
                break;
            case 'error':
                this.okBtn.classList.add('bg-red-500', 'hover:bg-red-700', 'focus:ring-red-300');
                break;
            case 'warning':
                this.okBtn.classList.add('bg-yellow-500', 'hover:bg-yellow-700', 'focus:ring-yellow-300');
                break;
            case 'info':
            default:
                this.okBtn.classList.add('bg-blue-500', 'hover:bg-blue-700', 'focus:ring-blue-300');
                break;
        }
    }

    hide() {
        this.modal.classList.add('hidden');
    }

    // Convenience methods
    success(title, message) {
        return this.show('success', title, message);
    }

    error(title, message) {
        return this.show('error', title, message);
    }

    warning(title, message) {
        return this.show('warning', title, message);
    }

    info(title, message) {
        return this.show('info', title, message);
    }
}

// Initialize custom alert
const customAlert = new CustomAlert();
document.addEventListener('DOMContentLoaded', function() {
    // Set default date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('date-filter').value = today;

    // Elements
    const dateFilter = document.getElementById('date-filter');
    const venueFilter = document.getElementById('venue-filter');
    const limitFilter = document.getElementById('limit-filter');
    const searchFilter = document.getElementById('search-filter');
    const filterBtn = document.getElementById('filter-btn');
    const tableBody = document.getElementById('booking-table-body');
    const paginationInfo = document.getElementById('pagination-info');

    // Modal elements
    const modal = document.getElementById('detail-modal');
    const closeModal = document.getElementById('close-modal');
    const cancelBtn = document.getElementById('cancel-btn');

    // Event listeners for filters
    dateFilter.addEventListener('change', loadBookings);
    venueFilter.addEventListener('change', loadBookings);
    limitFilter.addEventListener('change', loadBookings);
    filterBtn.addEventListener('click', loadBookings);

    // Search with debounce
    let searchTimeout;
    searchFilter.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(loadBookings, 500);
    });

    // Modal event listeners
    closeModal.addEventListener('click', hideModal);
    cancelBtn.addEventListener('click', hideModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) hideModal();
    });

    // View detail button event delegation
    document.addEventListener('click', function(e) {
        if (e.target.closest('.view-detail-btn')) {
            const btn = e.target.closest('.view-detail-btn');
            const booking = JSON.parse(btn.dataset.booking);
            showModal(booking);
        }
    });

    function loadBookings() {
        // Show loading
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="px-4 py-8 text-center text-gray-500">
                    <div class="flex items-center justify-center space-x-2">
                        <svg class="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Loading bookings...</span>
                    </div>
                </td>
            </tr>
        `;

        // Prepare data
        const formData = new FormData();
        formData.append('date', dateFilter.value); // Send date in Y-m-d format directly
        formData.append('venue', venueFilter.value);
        formData.append('limit', limitFilter.value);
        formData.append('search', searchFilter.value);
        formData.append('_token', '{{ csrf_token() }}');

        // Make AJAX request
        fetch('{{ route("admin.booking-listPost") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.bookings && data.bookings.length > 0) {
                    let html = '';
                    data.bookings.forEach(booking => {
                        html += createBookingRow(booking);
                    });
                    tableBody.innerHTML = html;
                    paginationInfo.innerHTML = `Showing ${data.showing_count} results`;
                } else {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="8" class="px-4 py-12 text-center">
                                <div class="flex flex-col items-center justify-center space-y-3">
                                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <div class="text-gray-500">
                                        <p class="text-lg font-medium">No Booking List data available</p>
                                        <p class="text-sm">There are no bookings for the selected date and filters.</p>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    `;
                    paginationInfo.innerHTML = 'No results found';
                }
            } else {
                throw new Error(data.error || 'Unknown error occurred');
            }
        })
        .catch(error => {
            console.error('Error loading bookings:', error);
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="px-4 py-8 text-center text-red-500">
                        <div class="flex flex-col items-center space-y-2">
                            <svg class="w-12 h-12 text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>Error: ${error.message}</span>
                        </div>
                    </td>
                </tr>
            `;
        });
    }

    function createBookingRow(booking) {
        return `
            <tr class="hover:bg-gray-50">
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${booking.tanggal_kunjungan}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${booking.nama_pengunjung}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${booking.qr_id}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${booking.nomor_whatsapp}</td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusClass(booking.status_tiket_color)}">${booking.status_tiket}</span>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${booking.tipe_tiket}</td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusClass(booking.status_kehadiran_color)}">${booking.status_kehadiran}</span>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div class="flex space-x-2">
                        <button class="view-detail-btn text-blue-600 hover:text-blue-900"
                                data-booking='${JSON.stringify(booking)}'
                                title="View Details">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                        <button class="text-green-600 hover:text-green-900" title="Mark as Checked In">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    function showModal(booking) {
        document.getElementById('modal-tanggal').textContent = booking.tanggal_kunjungan;
        document.getElementById('modal-nama').textContent = booking.nama_pengunjung;
        document.getElementById('modal-qr').textContent = booking.qr_id;
        document.getElementById('modal-whatsapp').textContent = booking.nomor_whatsapp;
        document.getElementById('modal-tipe').textContent = booking.tipe_tiket;
        document.getElementById('modal-checkin').textContent = booking.check_in_time;
        document.getElementById('modal-checkout').textContent = booking.check_out_time;

        // Set status badges
        const statusTiket = document.getElementById('modal-status-tiket');
        statusTiket.textContent = booking.status_tiket;
        statusTiket.className = `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusClass(booking.status_tiket_color)}`;

        const statusKehadiran = document.getElementById('modal-status-kehadiran');
        statusKehadiran.textContent = booking.status_kehadiran;
        statusKehadiran.className = `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusClass(booking.status_kehadiran_color)}`;

        modal.classList.remove('hidden');
    }

    function hideModal() {
        modal.classList.add('hidden');
    }

    function getStatusClass(color) {
        switch(color) {
            case 'green': return 'bg-green-100 text-green-800';
            case 'red': return 'bg-red-100 text-red-800';
            case 'yellow': return 'bg-yellow-100 text-yellow-800';
            case 'blue': return 'bg-blue-100 text-blue-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    }

    function formatDateForServer(dateString) {
        const date = new Date(dateString);
        const day = date.getDate();
        const month = date.toLocaleDateString('en-US', { month: 'long' });
        const year = date.getFullYear();
        return `${day} ${month} ${year}`;
    }

    // Auto-load bookings when page loads
    loadBookings();
});
</script>
@endpush