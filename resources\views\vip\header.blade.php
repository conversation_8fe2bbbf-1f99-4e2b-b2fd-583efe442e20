<header class="bg-kreen-dark border-b border-gray-800 sticky top-0 z-50 backdrop-blur-sm bg-opacity-95">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="{{ route('home') }}" class="flex items-center group">
                    <div class="w-[120px] h-[39px]">
                        <img src="{{ asset('image/kreenvip/kreenvip.png') }}" alt="KREEN VIP" class="w-full h-full object-contain" />
                    </div>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <nav class="hidden md:flex items-center space-x-8  hide-below-910">
                <a href="{{ route('home') }}" class="text-white hover:text-kreen-gold transition-colors duration-200 font-medium {{ request()->routeIs('home') ? 'text-kreen-gold!' : '' }}">
                    HOME
                </a>
                <a href="{{ route('venue.exploreNights') }}" class="text-white hover:text-kreen-gold transition-colors duration-200 font-medium {{ request()->routeIs('venue.exploreNights') ? 'text-kreen-gold!' : '' }}">
                    EXPLORE NIGHTS
                </a>
                <a href="{{ route('event.exploreEvents') }}" class="text-white hover:text-kreen-gold transition-colors duration-200 font-medium {{ request()->routeIs('event.exploreEvents') ? 'text-kreen-gold!' : '' }}">
                    EVENTS
                </a>
                <a href="{{ route('dj.listDj') }}" class="text-white hover:text-kreen-gold transition-colors duration-200 font-medium {{ request()->routeIs('dj.listDj') ? 'text-kreen-gold!' : '' }}">
                    MEET THE DJ
                </a>
            </nav>

            <!-- Right Side - Auth & Mobile Menu -->
            <div class="flex items-center space-x-4">
                @auth
                    <!-- Desktop Profile Dropdown -->
                    <div class="relative justify-center hidden md:flex">
                        <div id="profileButton" class="cursor-pointer flex items-center space-x-3 border border-kreen-gold px-3 py-1 rounded-full text-white relative z-20">
                            <img src="{{ Auth::user()->profile_picture ? Storage::url(Auth::user()->profile_picture) : 'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_960_720.png' }}"
                                alt="Profile Picture"
                                class="w-8 h-8 rounded-full object-cover border-2" />
                            <span class="font-medium text-white">{{ Auth::user()->name }}</span>
                        </div>
                        <div id="profileDropdown"
                            class="absolute left-1/2 top-full mt-2 w-40 bg-black border border-kreen-gold rounded-xl text-white hidden z-10 transform -translate-x-1/2 overflow-hidden">
                            <a href="/my-order" class="block px-4 py-2 hover:bg-kreen-gold hover:text-black transition-all">My Order</a>
                            <a href="/settings" class="block px-4 py-2 hover:bg-kreen-gold hover:text-black transition-all">Setting</a>
                            <form action="{{ route('auth.logout') }}" method="POST" class="block">
                                @csrf
                                <button type="submit" class="w-full text-left px-4 py-2 hover:bg-kreen-gold hover:text-black transition-all">Logout</button>
                            </form>
                        </div>
                    </div>

                    <!-- Mobile Profile Icon (Only Image) -->
                    <div class="md:hidden">
                        <img src="{{ Auth::user()->profile_picture ? Storage::url(Auth::user()->profile_picture) : 'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_960_720.png' }}"
                            alt="Profile Picture"
                            class="w-8 h-8 rounded-full object-cover border-2 border-kreen-gold" />
                    </div>
                @else
                    <!-- Login Button for Desktop -->
                    <a href="{{ route('auth.login') }}" class="hidden md:inline-block bg-kreen-gold hover:bg-kreen-gold-hover text-black font-semibold px-6 py-2 rounded-lg transition-all duration-200 transform hover:scale-105">
                        Log in
                    </a>
                @endauth

                <!-- Mobile Menu Button -->
                <button type="button" class="md:hidden text-white hover:text-kreen-gold transition-colors" onclick="toggleMobileMenu()">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 border-t border-gray-800 bg-kreen-dark">
                <!-- Navigation Links -->
                <a href="{{ route('home') }}" class="block px-3 py-2 text-white hover:text-kreen-gold transition-colors font-medium {{ request()->routeIs('home') ? 'text-kreen-gold!' : '' }}">
                    HOME
                </a>
                <a href="{{ route('venue.exploreNights') }}" class="block px-3 py-2 text-white hover:text-kreen-gold transition-colors font-medium {{ request()->routeIs('venue.exploreNights') ? 'text-kreen-gold!' : '' }}">
                    EXPLORE NIGHTS
                </a>
                <a href="{{ route('event.exploreEvents') }}" class="block px-3 py-2 text-white hover:text-kreen-gold transition-colors font-medium {{ request()->routeIs('event.exploreEvents') ? 'text-kreen-gold!' : '' }}">
                    EVENTS
                </a>
                <a href="{{ route('dj.listDj') }}" class="block px-3 py-2 text-white hover:text-kreen-gold transition-colors font-medium {{ request()->routeIs('dj.listDj') ? 'text-kreen-gold!' : '' }}">
                    MEET THE DJ
                </a>

                <!-- Mobile Auth Section -->
                @auth
                    <!-- Divider -->
                    <div class="border-t border-gray-700 my-2"></div>

                    <!-- User Menu Items -->
                    <a href="/my-order" class="block px-3 py-2 text-white hover:text-kreen-gold transition-colors font-medium">
                        My Order
                    </a>
                    <a href="/settings" class="block px-3 py-2 text-white hover:text-kreen-gold transition-colors font-medium">
                        Settings
                    </a>
                    <form action="{{ route('auth.logout') }}" method="POST" class="block">
                        @csrf
                        <button type="submit" class="w-full text-left px-3 py-2 text-white hover:text-kreen-gold transition-colors font-medium">
                            Logout
                        </button>
                    </form>

                    <!-- User Info -->
                    <div class="px-3 py-2">
                        <div class="flex items-center space-x-3">
                            <img src="{{ Auth::user()->profile_picture ? Storage::url(Auth::user()->profile_picture) : 'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_960_720.png' }}"
                                alt="Profile Picture"
                                class="w-10 h-10 rounded-full object-cover border-2 border-kreen-gold" />
                            <div>
                                <p class="text-white font-medium">{{ Auth::user()->name }}</p>
                                <p class="text-gray-400 text-sm">{{ Auth::user()->email }}</p>
                            </div>
                        </div>
                    </div>

                @else
                    <!-- Login Button for Mobile -->
                    <div class="border-t border-gray-700 my-2"></div>
                    <a href="{{ route('auth.login') }}" class="block mx-3 my-2 bg-kreen-gold hover:bg-kreen-gold-hover text-black font-semibold px-4 py-2 rounded-lg transition-all duration-200 text-center">
                        Log in
                    </a>
                @endauth
            </div>
        </div>
    </div>

    <!-- Gradient Border Bottom -->
    <div class="h-[1px] w-full bg-gradient-to-r from-[#A37A1D] via-[#FFEC95] to-[#281A00]"></div>
</header>

<script>
// Desktop Profile Dropdown
const profileButton = document.getElementById('profileButton');
const dropdown = document.getElementById('profileDropdown');

if (profileButton && dropdown) {
    // Toggle dropdown when button is clicked
    profileButton.addEventListener('click', function (event) {
        dropdown.classList.toggle('hidden');
        event.stopPropagation();
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function (event) {
        const isClickInside = profileButton.contains(event.target) || dropdown.contains(event.target);
        if (!isClickInside) {
            dropdown.classList.add('hidden');
        }
    });
}

// Mobile Menu Toggle
function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    if (mobileMenu) {
        mobileMenu.classList.toggle('hidden');
    }
}

// Close mobile menu when clicking on navigation links
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileLinks = mobileMenu?.querySelectorAll('a');

    if (mobileLinks) {
        mobileLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.add('hidden');
            });
        });
    }
});
</script>
