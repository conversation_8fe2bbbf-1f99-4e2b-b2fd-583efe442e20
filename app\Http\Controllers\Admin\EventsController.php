<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class EventsController extends Controller
{
    public function index(Request $request)
    {
        // Get merchant ID from authenticated user
        $user = Auth::user();
        $merchantId = $user->id_merchant ?? null;

        // For testing: use a default merchant ID if not available
        if (!$merchantId) {
            abort(404);
        } 

        // Get filter parameters
        $search = $request->input('search', '');
        $status = $request->input('status', '');
        $selectedVenue = $request->input('venue', 'all');

        // Build query with venue join
        $query = DB::table('tbl_bar_event as e')
            ->leftJoin('tbl_bar_venue as v', 'e.id_venue', '=', 'v.id')
            ->select(
                'e.*',
                'v.nama as venue_nama'
            );

        // Filter by merchant if available
        if ($merchantId) {
            $query->where('v.id_merchant', $merchantId);
        }

        // Add search filter
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('e.nama', 'like', "%{$search}%")
                  ->orWhere('e.lokasi', 'like', "%{$search}%")
                  ->orWhere('e.deskripsi', 'like', "%{$search}%")
                  ->orWhere('v.nama', 'like', "%{$search}%");
            });
        }

        // Add status filter
        if (!empty($status) && $status !== 'all') {
            $query->where('e.flag_aktif', $status);
        }

        // Add venue filter
        if (!empty($selectedVenue) && $selectedVenue !== 'all') {
            $query->where('e.id_venue', $selectedVenue);
        }

        // Get events
        $events = $query->orderBy('e.created_at', 'desc')
            ->get()
            ->map(fn ($event) => [
                    'id' => $event->id,
                    'tanggal_event' => $event->tanggal ? Carbon::parse($event->tanggal)->format('l, d F Y') : '-',
                    'poster_event' => $event->gambar ?? '/images/default-event.jpg',
                    'nama_event' => $event->nama ?? '-',
                    'penjelasan_singkat' => $event->deskripsi ? Str::limit($event->deskripsi, 100) : '-',
                    'status' => $this->getStatusLabel($event->flag_aktif),
                    'status_color' => $this->getStatusColor($event->flag_aktif),
                    'lokasi' => $event->lokasi ?? '-',
                    'venue_nama' => $event->venue_nama ?? '-',
                    'tanggal_raw' => $event->tanggal,
                    'deskripsi_full' => $event->deskripsi ?? '',
                    'flag_aktif' => $event->flag_aktif,
                    'id_venue' => $event->id_venue,
                    'id_provinsi' => $event->id_provinsi,
                    'id_kota' => $event->id_kota,
                    'id_kelurahan' => $event->id_kelurahan
                ]);

        // Get venues for dropdown (filtered by merchant)
        $venuesQuery = DB::table('tbl_bar_venue')
            ->where('flag_aktif', '1')
            ->select('id', 'nama');

        if ($merchantId) {
            $venuesQuery->where('id_merchant', $merchantId);
        }

        $venues = $venuesQuery->orderBy('nama')->get();

        // Get provinces for dropdown (assuming Indonesia as default country)
        $provinces = DB::table('kreen_production_online.tbl_adm_province')
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        // Return JSON for AJAX requests
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'events' => $events->toArray(),
                'venues' => $venues->toArray(),
                'provinces' => $provinces->toArray(),
                'total_count' => $events->count()
            ]);
        }

        // Return view for regular requests
        $events = $events->toArray();
        $provinces = $provinces->toArray();
        return view('admin.events', compact('events', 'venues', 'provinces', 'search', 'status', 'selectedVenue'));
    }

    public function getVenueData($venueId)
    {
        try {
            Log::info('getVenueData called for venue auto-complete', ['venueId' => $venueId]);

            // Simple query - just get venue data, no event dependency
            $venue = DB::table('tbl_bar_venue')
                ->where('id', $venueId)
                ->where('flag_aktif', '1')
                ->select('id','id_negara', 'id_propinsi', 'id_kota', 'id_kelurahan', 'alamat', 'lat', 'lng', 'id_merchant')
                ->first();

            Log::info('Venue found', ['venue_exists' => $venue ? 'yes' : 'no']);

            if (!$venue) {
                Log::info('Venue not found, returning empty data for manual input');
                return response()->json([
                    'success' => true,
                    'venue' => [
                        'id_venue' => null,
                        'id_negara' => null,
                        'id_propinsi' => null,
                        'id_kota' => null,
                        'id_kelurahan' => null,
                        'alamat' => null,
                        'lat' => null,
                        'lng' => null
                    ],
                    'message' => 'Venue tidak ditemukan, silakan isi data lokasi manual.'
                ]);
            }

            // Return venue data for auto-complete (no merchant check for now to avoid complexity)
            Log::info('Returning venue data', [
                'has_provinsi' => $venue->id_propinsi ? 'yes' : 'no',
                'has_kota' => $venue->id_kota ? 'yes' : 'no',
                'has_alamat' => $venue->alamat ? 'yes' : 'no'
            ]);

            return response()->json([
                'success' => true,
                'venue' => [
                    'id_venue' => $venue->id ?? null,
                    'id_negara' => $venue->id_negara ?? null,
                    'id_propinsi' => $venue->id_propinsi ?? null,
                    'id_kota' => $venue->id_kota ?? null,
                    'id_kelurahan' => $venue->id_kelurahan ?? null,
                    'alamat' => $venue->alamat ?? null,
                    'lat' => $venue->lat ?? null,
                    'lng' => $venue->lng ?? null
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error in getVenueData: ' . $e->getMessage());
            return response()->json([
                'success' => true,
                'venue' => [
                    'id_negara' => null,
                    'id_propinsi' => null,
                    'id_kota' => null,
                    'id_kelurahan' => null,
                    'alamat' => null,
                    'lat' => null,
                    'lng' => null
                ],
                'message' => 'Error loading venue data, silakan isi data lokasi manual.'
            ]);
        }
    }

    public function getDjs()
    {
        try {
            Log::info('getDjs method called');
    
            // Cek apakah tabel ada
            $tableExists = DB::select("SHOW TABLES LIKE 'tbl_bar_dj'");
            if (empty($tableExists)) {
                Log::error('Table tbl_bar_dj does not exist');
                return response()->json([
                    'success' => false,
                    'message' => 'DJ table not found'
                ], 500);
            }
    
            // Ambil data DJ yang aktif
            $djs = DB::table('tbl_bar_dj')
                ->where('flag_aktif', '1')
                ->select('id', 'nama', 'origin', 'gender', 'gambar')
                ->orderBy('nama', 'asc')
                ->get();
    
            Log::info('DJs found: ' . $djs->count());
    
            return response()->json([
                'success' => true,
                'djs' => $djs,
                'count' => $djs->count()
            ]);
        } catch (\Exception $e) {
            Log::error('Error in getDjs: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }
    

    public function store(Request $request)
    {
        try {
            // Get merchant ID from authenticated user
            $user = Auth::user();
            $merchantId = $user->id_merchant ?? null;

            $request->validate([
                'nama' => 'required|string|max:255',
                'tanggal' => 'required|date',
                'lokasi' => 'required|string|max:255',
                'id_venue' => 'nullable|string|max:32',
                'id_negara' => 'nullable|string|max:20',
                'id_provinsi' => 'nullable|string|max:20',
                'id_kota' => 'nullable|string|max:20',
                'id_kelurahan' => 'nullable|string|max:20',
                'flag_aktif' => 'required|in:0,1',
                'deskripsi' => 'nullable|string',
                'gambar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'selected_djs' => 'nullable|array',
                'selected_djs.*' => 'exists:tbl_bar_dj,id'
            ]);

            // Validate venue belongs to merchant
            if ($merchantId && $request->id_venue) {
                $venueExists = DB::table('tbl_bar_venue')
                    ->where('id', $request->id_venue)
                    ->where('id_merchant', $merchantId)
                    ->where('flag_aktif', '1')
                    ->exists();

                if (!$venueExists) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Venue tidak valid atau tidak dimiliki oleh merchant ini.'
                    ], 403);
                }
            }

            $eventId = (int) substr(str_replace('.', '', microtime(true)), 0, 10);
            $slug = Str::slug($request->nama);

            // Handle image upload
            $imagePath = null;
            if ($request->hasFile('gambar')) {
                $image = $request->file('gambar');
                $imageName = time() . '_' . $image->getClientOriginalName();
                $image->move(public_path('uploads/events'), $imageName);
                $imagePath = '/uploads/events/' . $imageName;
            }

            // Insert event
            DB::table('tbl_bar_event')->insert([
                'id' => $eventId,
                'id_venue' => $request->id_venue,
                'nama' => $request->nama,
                'tanggal' => $request->tanggal,
                'slug' => $slug,
                'lokasi' => $request->lokasi,
                'deskripsi' => $request->deskripsi,
                'gambar' => $imagePath,
                'id_negara' => $request->id_negara,
                'id_provinsi' => $request->id_provinsi,
                'id_kota' => $request->id_kota,
                'id_kelurahan' => $request->id_kelurahan,
                'flag_aktif' => $request->flag_aktif,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Handle DJ assignments
            if ($request->selected_djs && is_array($request->selected_djs)) {
                $djPivotData = [];
                foreach ($request->selected_djs as $djId) {
                    $djPivotData[] = [
                        'id' => hexdec(substr(uniqid(), -8)),
                        'id_event' => $eventId,
                        'id_dj' => $djId,
                        'created_at' => now(),
                        'updated_at' => now()
                    ];
                }

                if (!empty($djPivotData)) {
                    DB::table('tbl_piv_event_dj')->insert($djPivotData);
                }
            }

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Event berhasil ditambahkan!'
                ]);
            }

            return redirect()->route('admin.events')->with('success', 'Event berhasil ditambahkan!');

        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    public function update(Request $request, $id)
    {
        try {
            // Get merchant ID from authenticated user
            $user = Auth::user();
            $merchantId = $user->id_merchant ?? null;

            $request->validate([
                'nama' => 'required|string|max:255',
                'tanggal' => 'required|date',
                'lokasi' => 'required|string|max:255',
                'id_venue' => 'nullable|string|max:32',
                'id_negara' => 'nullable|string|max:20',
                'id_provinsi' => 'nullable|string|max:20',
                'id_kota' => 'nullable|string|max:20',
                'id_kelurahan' => 'nullable|string|max:20',
                'flag_aktif' => 'required|in:0,1',
                'deskripsi' => 'nullable|string',
                'gambar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'selected_djs' => 'nullable|array',
                'selected_djs.*' => 'exists:tbl_bar_dj,id'
            ]);

            // Get event with venue information
            $event = DB::table('tbl_bar_event as e')
                ->leftJoin('tbl_bar_venue as v', 'e.id_venue', '=', 'v.id')
                ->select('e.*', 'v.id_merchant')
                ->where('e.id', $id)
                ->first();

            if (!$event) {
                return response()->json([
                    'success' => false,
                    'message' => 'Event tidak ditemukan!'
                ], 404);
            }

            // Check if event belongs to merchant
            if ($merchantId && $event->id_merchant != $merchantId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda tidak memiliki akses untuk mengubah event ini.'
                ], 403);
            }

            // Validate venue belongs to merchant if venue is being changed
            if ($merchantId && $request->id_venue && $request->id_venue != $event->id_venue) {
                $venueExists = DB::table('tbl_bar_venue')
                    ->where('id', $request->id_venue)
                    ->where('id_merchant', $merchantId)
                    ->where('flag_aktif', '1')
                    ->exists();

                if (!$venueExists) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Venue tidak valid atau tidak dimiliki oleh merchant ini.'
                    ], 403);
                }
            }

            $slug = Str::slug($request->nama);

            // Handle image upload
            $imagePath = $event->gambar; // Keep existing image by default
            if ($request->hasFile('gambar')) {
                // Delete old image if exists
                if ($event->gambar && file_exists(public_path($event->gambar))) {
                    unlink(public_path($event->gambar));
                }

                $image = $request->file('gambar');
                $imageName = time() . '_' . $image->getClientOriginalName();
                $image->move(public_path('uploads/events'), $imageName);
                $imagePath = '/uploads/events/' . $imageName;
            }

            // Update event
            DB::table('tbl_bar_event')->where('id', $id)->update([
                'id_venue' => $request->id_venue,
                'nama' => $request->nama,
                'tanggal' => $request->tanggal,
                'slug' => $slug,
                'lokasi' => $request->lokasi,
                'deskripsi' => $request->deskripsi,
                'gambar' => $imagePath,
                'id_negara' => $request->id_negara,
                'id_provinsi' => $request->id_provinsi,
                'id_kota' => $request->id_kota,
                'id_kelurahan' => $request->id_kelurahan,
                'flag_aktif' => $request->flag_aktif,
                'updated_at' => now()
            ]);

            // Update DJ assignments
            // First, remove existing DJ assignments
            DB::table('tbl_piv_event_dj')->where('id_event', $id)->delete();

            // Then, add new DJ assignments
            if ($request->selected_djs && is_array($request->selected_djs)) {
                $djPivotData = [];
                foreach ($request->selected_djs as $djId) {
                    $djPivotData[] = [
                        'id' => hexdec(substr(uniqid(), -8)),
                        'id_event' => $id,
                        'id_dj' => $djId,
                        'created_at' => now(),
                        'updated_at' => now()
                    ];
                }

                if (!empty($djPivotData)) {
                    DB::table('tbl_piv_event_dj')->insert($djPivotData);
                }
            }

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Event berhasil diupdate!'
                ]);
            }

            return redirect()->route('admin.events')->with('success', 'Event berhasil diupdate!');

        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    public function show($id)
    {
        try {
            Log::info('show method called', ['id' => $id]);

            // Safety check - if ID looks like a route name, reject it
            if (in_array($id, ['regencies', 'districts', 'provinces', 'djs', 'venues'])) {
                Log::error('show method called with route name instead of ID', ['id' => $id]);
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid event ID - route conflict detected'
                ], 400);
            }

            // Get merchant ID from authenticated user
            $user = Auth::user();
            $merchantId = $user->id_merchant ?? null;

            // Get event with venue information
            $event = DB::table('tbl_bar_event as e')
                ->leftJoin('tbl_bar_venue as v', 'e.id_venue', '=', 'v.id')
                ->select('e.*', 'v.nama as venue_nama', 'v.id_merchant')
                ->where('e.id', $id)
                ->first();

            Log::info('Event query result', ['event_found' => $event ? 'yes' : 'no']);

            if (!$event) {
                return response()->json([
                    'success' => false,
                    'message' => 'Event tidak ditemukan.'
                ], 404);
            }

            // Check if event belongs to merchant (if merchant ID is available)
            if ($merchantId && $event->id_venue && $event->id_merchant !== $merchantId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda tidak memiliki akses ke event ini.'
                ], 403);
            }

            // Get assigned DJs
            $assignedDjs = DB::table('tbl_piv_event_dj as ed')
                ->join('tbl_bar_dj as d', 'ed.id_dj', '=', 'd.id')
                ->where('ed.id_event', $id)
                ->select('d.id', 'd.nama', 'd.origin', 'd.gender', 'd.gambar')
                ->get();

            return response()->json([
                'success' => true,
                'event' => $event,
                'assigned_djs' => $assignedDjs
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            // Get merchant ID from authenticated user
            $user = Auth::user();
            $merchantId = $user->id_merchant ?? null;

            // Get event with venue information
            $event = DB::table('tbl_bar_event as e')
                ->leftJoin('tbl_bar_venue as v', 'e.id_venue', '=', 'v.id')
                ->select('e.*', 'v.id_merchant')
                ->where('e.id', $id)
                ->first();

            // if (!$event) {
            //     return response()->json([
            //         'success' => false,
            //         'message' => 'Event tidak ditemukan!'
            //     ], 404);
            // }

            // Check if event belongs to merchant
            if ($merchantId && $event->id_merchant != $merchantId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda tidak memiliki akses untuk menghapus event ini.'
                ], 403);
            }

            // Delete image file if exists
            if ($event->gambar && file_exists(public_path($event->gambar))) {
                unlink(public_path($event->gambar));
            }

            // Delete event
            DB::table('tbl_bar_event')->where('id', $id)->delete();

            return response()->json([
                'success' => true,
                'message' => 'Event berhasil dihapus!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getVenues(Request $request)
    {
        // Get merchant ID from authenticated user
        $user = Auth::user();
        $merchantId = $user->id_merchant ?? null;

        $search = $request->input('search', '');

        $query = DB::table('tbl_bar_venue')
            ->where('flag_aktif', '1')
            ->select('id', 'nama', 'alamat', 'no_wa');

        // Filter by merchant if available
        if ($merchantId) {
            $query->where('id_merchant', $merchantId);
        }

        if (!empty($search)) {
            $query->where('nama', 'like', "%{$search}%");
        }

        $venues = $query->orderBy('nama')->limit(10)->get();

        return response()->json([
            'success' => true,
            'venues' => $venues->toArray()
        ]);
    }

    public function getProvinces(Request $request)
    {
        try {
            $countryId = $request->input('country_id');

            Log::info('getProvinces called', ['country_id' => $countryId]);

            $query = DB::connection('kreen_production_online')->table('tbl_adm_province')
                ->select('id', 'name');

            if ($countryId) {
                $query->where('id_country', $countryId);
            }

            $provinces = $query->orderBy('name')->get();

            Log::info('Provinces found', ['count' => $provinces->count()]);

            return response()->json([
                'success' => true,
                'provinces' => $provinces->toArray()
            ]);

        } catch (\Exception $e) {
            Log::error('Error in getProvinces: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getRegencies(Request $request)
    {
        try {
            $provinceId = $request->input('province_id');

            Log::info('getRegencies called', ['province_id' => $provinceId]);

            if (!$provinceId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Province ID is required'
                ], 400);
            }

            $regencies = DB::connection('kreen_production_online')->table('tbl_adm_regency')
                ->where('id_province', $provinceId)
                ->select('id', 'name', 'nickname')
                ->orderBy('name')
                ->get();

            Log::info('Regencies found', ['count' => $regencies->count()]);

            return response()->json([
                'success' => true,
                'regencies' => $regencies->toArray()
            ]);

        } catch (\Exception $e) {
            Log::error('Error in getRegencies: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getDistricts(Request $request)
    {
        try {
            $regencyId = $request->input('regency_id');

            Log::info('getDistricts called', ['regency_id' => $regencyId]);

            if (!$regencyId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Regency ID is required'
                ], 400);
            }

            $districts = DB::connection('kreen_production_online')->table('tbl_adm_district')
                ->where('id_regency', $regencyId)
                ->select('id', 'name')
                ->orderBy('name')
                ->get();

            Log::info('Districts found', ['count' => $districts->count()]);

            return response()->json([
                'success' => true,
                'districts' => $districts->toArray()
            ]);

        } catch (\Exception $e) {
            Log::error('Error in getDistricts: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }

    private function getStatusLabel($flag)
    {
        return $flag == '1' ? 'Aktif' : 'Tidak Aktif';
    }

    private function getStatusColor($flag)
    {
        return $flag == '1' ? 'green' : 'red';
    }
}
