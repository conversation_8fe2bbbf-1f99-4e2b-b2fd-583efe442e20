@extends('vip.app')

@section('title', 'Booking Checkout - KREEN VIP')

@push('styles')
<style>
.sticky-card {
    position: sticky;
    top: 10rem;
    z-index: 10;
}

.booking-container {
    position: relative;
    overflow: visible;
}

.booking-grid {
    position: relative;
    overflow: visible;
}

/* Full width section */
.full-width-section {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    padding-left: calc(50vw - 50%);
    padding-right: calc(50vw - 50%);
}

/* Header with background image */
.header-bg {
    background-image: url('https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    position: relative;
}

.header-bg::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5));
    z-index: 1;
}

.header-content {
    position: relative;
    z-index: 2;
}

/* Mobile sticky adjustments */
@media (max-width: 1024px) {
    .sticky-card {
        position: relative;
        top: auto;
    }
}

/* Remove any bottom margins/padding */
.full-width-section:last-child {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

body {
    margin-bottom: 0 !important;
}

/* Ensure the main content area has no bottom spacing */
.explore-nights-bg-container:last-child {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

/* Progress indicator responsive design */
@media (max-width: 768px) {
    .booking-progress {
        display: none !important;
    }
    
    /* Show mobile version */
    .booking-progress-mobile {
        display: flex !important;
        justify-content: center;
        padding: 0.5rem 0;
        border-top: 1px solid #374151;
        margin-top: 1rem;
    }
}

@media (min-width: 769px) {
    .booking-progress-mobile {
        display: none !important;
    }
}
</style>
@endpush

@section('content')
<!-- Full Width Header with Background Image -->
<section class="full-width-section header-bg py-16">
    <div class="header-content text-center">
        <h1 class="text-4xl md:text-6xl font-bold text-white mb-4">HELEN'S NIGHT MART <span class="text-purple-400">GADING SERPONG</span></h1>
        <p class="text-gray-300 text-lg">Jl. Gading Serpong Boulevard No. 3 Bez Plaza, Curug Banten, Tangerang 15810 Indonesia</p>
    </div>
</section>

<!-- Full Width Personal Information Section -->
<section class="full-width-section py-16 bg-gradient-to-b from-black to-gray-900">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-12 booking-grid">
            <!-- Left Side - Form (8 columns) -->
            <div class="lg:col-span-8 space-y-8">
                <!-- Personal Information -->
                <div>
                    <h2 class="text-white text-2xl font-bold mb-6">Fill Personal Information</h2>
                    
                    <!-- Guest Detail Info -->
                    <div class="mb-6">
                        <p class="text-gray-400 text-sm mb-1">Guest Data 1</p>
                        <p class="text-white font-medium text-lg">FDC Monday & Tuesday</p>
                    </div>

                    <!-- Form Fields -->
                    <div class="space-y-6">
                        <!-- Full Name -->
                        <div>
                            <label class="block text-white text-sm font-medium mb-2">
                                Full Name <span class="text-red-500">*</span>
                            </label>
                            <input 
                                type="text" 
                                placeholder="Enter your full name"
                                class="w-full bg-transparent border-2 border-kreen-gold text-white px-4 py-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-kreen-gold placeholder-gray-400"
                            >
                        </div>

                        <!-- Email -->
                        <div>
                            <label class="block text-white text-sm font-medium mb-2">
                                Email <span class="text-red-500">*</span>
                            </label>
                            <input 
                                type="email" 
                                placeholder="Enter your email address"
                                class="w-full bg-transparent border-2 border-kreen-gold text-white px-4 py-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-kreen-gold placeholder-gray-400"
                            >
                        </div>

                        <!-- Phone Number -->
                        <div>
                            <label class="block text-white text-sm font-medium mb-2">
                                Phone Number <span class="text-red-500">*</span>
                            </label>
                            <input 
                                type="tel" 
                                placeholder="Enter your phone number"
                                class="w-full bg-transparent border-2 border-kreen-gold text-white px-4 py-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-kreen-gold placeholder-gray-400"
                            >
                        </div>
                    </div>
                </div>

                <!-- Payment Method -->
                <div>
                    <h2 class="text-white text-2xl font-bold mb-6">Choose Payment Method</h2>
                    
                    <div class="space-y-6">
                        <!-- Virtual Account -->
                        <div>
                            <h3 class="text-white text-lg font-semibold mb-4">Virtual Account</h3>
                            <div class="space-y-3">
                                <label class="flex items-center p-3 rounded-lg cursor-pointer hover:border-kreen-gold transition-colors">
                                    <input type="radio" name="payment" value="bca" class="w-5 h-5 text-gray-400 bg-transparent border-2 border-gray-400 mr-4">
                                    <img src="https://upload.wikimedia.org/wikipedia/commons/5/5c/Bank_Central_Asia.svg" alt="BCA" class="w-12 h-8 object-contain mr-3">
                                    <span class="text-white">Virtual Account BCA</span>
                                </label>
                                
                                <label class="flex items-center p-3 rounded-lg cursor-pointer hover:border-kreen-gold transition-colors">
                                    <input type="radio" name="payment" value="bri" class="w-5 h-5 text-gray-400 bg-transparent border-2 border-gray-400 mr-4">
                                    <img src="https://upload.wikimedia.org/wikipedia/commons/2/2e/BRI_2020.svg" alt="BRI" class="w-12 h-8 object-contain mr-3">
                                    <span class="text-white">Virtual Account BRI</span>
                                </label>
                                
                                <label class="flex items-center p-3 rounded-lg cursor-pointer hover:border-kreen-gold transition-colors">
                                    <input type="radio" name="payment" value="mandiri" class="w-5 h-5 text-gray-400 bg-transparent border-2 border-gray-400 mr-4">
                                    <img src="https://upload.wikimedia.org/wikipedia/commons/a/ad/Bank_Mandiri_logo_2016.svg" alt="Mandiri" class="w-12 h-8 object-contain mr-3">
                                    <span class="text-white">Virtual Account Mandiri</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- E-wallet -->
                        <div>
                            <h3 class="text-white text-lg font-semibold mb-4">E-wallet</h3>
                            <div class="space-y-3">
                                <label class="flex items-center p-3 rounded-lg cursor-pointer hover:border-kreen-gold transition-colors">
                                    <input type="radio" name="payment" value="dana" class="w-5 h-5 text-gray-400 bg-transparent border-2 border-gray-400 mr-4">
                                    <img src="https://upload.wikimedia.org/wikipedia/commons/7/72/Logo_dana_blue.svg" alt="Dana" class="w-12 h-8 object-contain mr-3">
                                    <span class="text-white">Dana</span>
                                </label>
                                
                                <label class="flex items-center p-3 rounded-lg cursor-pointer hover:border-kreen-gold transition-colors">
                                    <input type="radio" name="payment" value="linkaja" class="w-5 h-5 text-gray-400 bg-transparent border-2 border-gray-400 mr-4">
                                    <img src="https://upload.wikimedia.org/wikipedia/commons/8/85/LinkAja.svg" alt="LinkAja" class="w-12 h-8 object-contain mr-3">
                                    <span class="text-white">LinkAja</span>
                                </label>
                                
                                <label class="flex items-center p-3 rounded-lg cursor-pointer hover:border-kreen-gold transition-colors">
                                    <input type="radio" name="payment" value="ovo" class="w-5 h-5 text-gray-400 bg-transparent border-2 border-gray-400 mr-4">
                                    <img src="https://upload.wikimedia.org/wikipedia/commons/e/eb/Logo_ovo_purple.svg" alt="OVO" class="w-12 h-8 object-contain mr-3">
                                    <span class="text-white">OVO</span>
                                </label>
                                
                                <label class="flex items-center p-3 rounded-lg cursor-pointer hover:border-kreen-gold transition-colors">
                                    <input type="radio" name="payment" value="shopee" class="w-5 h-5 text-gray-400 bg-transparent border-2 border-gray-400 mr-4">
                                    <img src="https://upload.wikimedia.org/wikipedia/commons/f/fe/Shopee.svg" alt="ShopeePay" class="w-12 h-8 object-contain mr-3">
                                    <span class="text-white">ShopeePay</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- QRIS -->
                        <div>
                            <h3 class="text-white text-lg font-semibold mb-4">QRIS</h3>
                            <label class="flex items-center p-3 rounded-lg cursor-pointer hover:border-kreen-gold transition-colors">
                                <input type="radio" name="payment" value="qris" class="w-5 h-5 text-gray-400 bg-transparent border-2 border-gray-400 mr-4">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/e/e1/QRIS_logo.svg" alt="QRIS" class="w-12 h-8 object-contain mr-3">
                                <span class="text-white">QRIS</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Terms & Conditions -->
                <div>
                    <h2 class="text-white text-2xl font-bold mb-6">Terms & Conditions</h2>
                    
                    <div class="space-y-4">
                        <label class="flex items-start space-x-3 cursor-pointer">
                            <input type="checkbox" class="mt-1 w-4 h-4 text-kreen-gold bg-transparent border-2 border-kreen-gold rounded focus:ring-kreen-gold">
                            <span class="text-gray-300 text-sm">
                                Saya telah membaca dan menyetujui syarat dan ketentuan yang berlaku dengan total pembayaran sebesar Rp 150.000
                            </span>
                        </label>
                        
                        <label class="flex items-start space-x-3 cursor-pointer">
                            <input type="checkbox" class="mt-1 w-4 h-4 text-kreen-gold bg-transparent border-2 border-kreen-gold rounded focus:ring-kreen-gold">
                            <span class="text-gray-300 text-sm">
                                Saya telah memahami dan menyetujui House Rules dari HELEN'S NIGHT MART GADING SERPONG
                            </span>
                        </label>
                        
                        <label class="flex items-start space-x-3 cursor-pointer">
                            <input type="checkbox" class="mt-1 w-4 h-4 text-kreen-gold bg-transparent border-2 border-kreen-gold rounded focus:ring-kreen-gold">
                            <span class="text-gray-300 text-sm">
                                Pembelian reservasi akan dikenai biaya. Saya telah membaca dan menyetujui Ketentuan Pembatalan.
                            </span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Right Side - Sticky Booking Summary (4 columns) -->
            <div class="lg:col-span-4">
                <div class="sticky-card border-2 border-kreen-gold rounded-xl p-6 space-y-6" style="background: linear-gradient(98.07deg, #000000 0%, #2E2E2E 25.48%, #000000 50.48%, #464646 81.73%, #040404 100%);">
                    <h3 class="text-white text-xl font-bold mb-4">Your Booking</h3>
                    
                    <!-- Venue -->
                    <div class="flex items-center space-x-3">
                        <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div>
                            <p class="text-white font-medium text-sm">HELEN'S NIGHT MART GADING SERPONG</p>
                        </div>
                    </div>

                    <!-- Date -->
                    <div class="flex items-center space-x-3">
                        <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div>
                            <p class="text-gray-400 text-sm">Tuesday, 25 Dec 2024</p>
                        </div>
                    </div>

                    <!-- Ticket -->
                    <div class="bg-white/20 bg-opacity-50 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 100 4v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2a2 2 0 100-4V6z"/>
                                </svg>
                            </div>
                            <div>
                                <p class="text-white text-sm font-medium">FDC Monday & Tuesday</p>
                                <p class="text-gray-400 text-xs">1x Guest</p>
                            </div>
                        </div>
                    </div>

                    <!-- Divider -->
                    <hr class="border-gray-600">

                    <!-- Payment Details -->
                    <div class="space-y-3">
                        <h4 class="text-white font-semibold">Payment Details</h4>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-400 text-sm">FDC Monday & Tuesday</span>
                            <span class="text-white text-sm">Rp 150.000</span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-400 text-sm">Service Fee</span>
                            <span class="text-white text-sm">Rp 5.000</span>
                        </div>
                        
                        <hr class="border-gray-600">
                        
                        <div class="flex justify-between">
                            <span class="text-white font-medium">Total Payment</span>
                            <span class="text-kreen-gold font-bold text-lg">Rp 155.000</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Full Width Booking Button Section - No bottom padding -->
<section class="full-width-section bg-gray-900" style="padding-top: 1rem; padding-bottom: 10rem;">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-12">
            <div class="lg:col-span-8">
                <button class="w-full bg-kreen-gold hover:bg-yellow-500 text-black font-bold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 text-lg mb-[50px]">
                    BOOKING NOW
                </button>
            </div>
            <div class="lg:col-span-4">
                <!-- Empty space to align with the form -->
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    // Payment method selection
    document.querySelectorAll('input[name="payment"]').forEach(radio => {
        radio.addEventListener('change', function() {
            // Remove active state from all payment options
            document.querySelectorAll('label').forEach(label => {
                if (label.querySelector('input[name="payment"]')) {
                    label.classList.remove('border-kreen-gold', 'bg-kreen-gold', 'bg-opacity-10');
                    label.classList.add('border-gray-600');
                }
            });
            
            // Add active state to selected payment option
            if (this.checked) {
                const label = this.closest('label');
                label.classList.remove('border-gray-600');
                label.classList.add('border-kreen-gold','bg-opacity-10');
            }
        });
    });

    // Form validation
    document.querySelector('.bg-kreen-gold').addEventListener('click', function(e) {
        e.preventDefault();
        
        const name = document.querySelector('input[type="text"]').value;
        const email = document.querySelector('input[type="email"]').value;
        const phone = document.querySelector('input[type="tel"]').value;
        const paymentMethod = document.querySelector('input[name="payment"]:checked');
        const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
        
        if (!name || !email || !phone) {
            alert('Please fill in all personal information fields');
            return;
        }
        
        if (!paymentMethod) {
            alert('Please select a payment method');
            return;
        }
        
        if (checkboxes.length < 3) {
            alert('Please accept all terms and conditions');
            return;
        }
        
        // Proceed with booking
        alert('Booking confirmed! Redirecting to payment...');
    });

    // Enhanced sticky behavior
    function handleStickyCard() {
        const stickyCard = document.querySelector('.sticky-card');
        const bookingContainer = document.querySelector('.booking-container');
        
        if (!stickyCard || !bookingContainer) return;
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    stickyCard.style.position = 'sticky';
                    stickyCard.style.top = '2rem';
                }
            });
        }, {
            threshold: 0.1
        });
        
        observer.observe(bookingContainer);
    }
    
    handleStickyCard();
});

// Header Navigation Replacement for Booking Flow
function replaceHeaderNavigation() {
    // Hide existing navigation
    const desktopNav = document.querySelector('nav.hidden.md\\:flex');
    const loginButton = document.querySelector('a[href=""].hidden.md\\:inline-block');
    
    if (desktopNav) {
        desktopNav.style.display = 'none';
    }
    if (loginButton) {
        loginButton.style.display = 'none';
    }
    
    // Create progress indicator
    const headerContainer = document.querySelector('header .flex.items-center.justify-between');
    if (headerContainer && !document.querySelector('.booking-progress')) {
        const progressContainer = document.createElement('div');
        progressContainer.className = 'booking-progress flex items-center space-x-8';
        progressContainer.innerHTML = `
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-kreen-gold rounded-full flex items-center justify-center">
                    <span class="text-black text-sm font-bold">1</span>
                </div>
                <span class="text-kreen-gold font-medium text-sm">Guest Info</span>
            </div>
            
            <div class="flex items-center">
                <div class="w-8 h-0.5 bg-gray-600"></div>
                <svg class="w-3 h-3 text-gray-600 mx-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
            </div>
            
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 border-2 border-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-gray-600 text-sm font-bold">2</span>
                </div>
                <span class="text-gray-600 font-medium text-sm">Payment</span>
            </div>
            
            <div class="flex items-center">
                <div class="w-8 h-0.5 bg-gray-600"></div>
                <svg class="w-3 h-3 text-gray-600 mx-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
            </div>
            
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 border-2 border-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-gray-600 text-sm font-bold">3</span>
                </div>
                <span class="text-gray-600 font-medium text-sm">You're In!</span>
            </div>
        `;
        
        // Insert progress indicator after logo
        const logoContainer = headerContainer.querySelector('.flex.items-center');
        if (logoContainer && logoContainer.nextElementSibling) {
            headerContainer.insertBefore(progressContainer, logoContainer.nextElementSibling);
        }
    }
}

// Initialize header replacement when page loads
replaceHeaderNavigation();

// Also handle if header loads after this script
setTimeout(replaceHeaderNavigation, 100);

// Add mobile progress indicator
function addMobileProgress() {
    const mobileMenu = document.querySelector('#mobile-menu');
    if (mobileMenu && !document.querySelector('.booking-progress-mobile')) {
        const mobileProgress = document.createElement('div');
        mobileProgress.className = 'booking-progress-mobile md:hidden flex items-center justify-center space-x-4 px-4 py-3 bg-kreen-dark border-t border-gray-800';
        mobileProgress.innerHTML = `
            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 bg-kreen-gold rounded-full flex items-center justify-center">
                    <span class="text-black text-xs font-bold">1</span>
                </div>
                <span class="text-kreen-gold font-medium text-[10px] md:text-xs">Info</span>
            </div>
            
            <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            
            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 border border-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-gray-600 text-xs font-bold">2</span>
                </div>
                <span class="text-gray-600 font-medium text-[10px] md:text-xs">Payment</span>
            </div>
            
            <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            
            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 border border-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-gray-600 text-xs font-bold">3</span>
                </div>
                <span class="text-gray-600 font-medium text-[10px] md:text-xs">In!</span>
            </div>
        `;
        
        mobileMenu.parentNode.insertBefore(mobileProgress, mobileMenu.nextSibling);
    }
}

// Add mobile progress
addMobileProgress();
setTimeout(addMobileProgress, 100);
</script>
@endpush
