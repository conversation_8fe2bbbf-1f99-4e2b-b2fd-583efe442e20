<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Cek apakah user sudah login dan role adalah admin
        if (Auth::check() && Auth::user()->role === 'admin') {
            // Jika admin mencoba akses area user, redirect ke admin dashboard
            return redirect()->route('admin.dashboard');
        }

        return $next($request);
    }
}
