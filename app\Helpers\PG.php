<?php

namespace App\Helpers;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class PG
{
    public static function getPaymentMethods(): Collection
    {
        return DB::table('tbl_pg_metod_payget as p')
            ->select('p.id', 'p.bank_code', 'p.payment_name', 'p.attribute', 'p.currency_pg', 'p.region', 'p.fee', 'p.fee_percent', 'p.ppn', 'p.limit_min', 'p.limit_max', 'p.img', 'c.category_name', 'c.code_category_payget')
            ->join('tbl_pg_category_payget as c', 'p.id_category_payget', '=', 'c.id')
            ->where('p.flag_aktif', '1')
            ->where('c.flag_aktif', '1')
            ->when(env('APP_ENV') == 'production', function ($query) {
                $query->where('p.flag_ready_live', '1');
            })
            ->orderBy('c.sort', 'ASC')
            ->orderBy('p.sort', 'ASC')
            ->get();
    }

    public static function getInstructionsByPaymentMethodId(string $id): Collection
    {
        $instructions = DB::table('tbl_pg_instruction_payget as ip')
            ->select(['ip.id', 'mip.metod_instruction_payget', 'ip.instruction_payget'])
            ->join('tbl_pg_metod_instruction_payget as mip', 'ip.id_metod_instruction_payget', 'mip.id')
            ->where('ip.id_metod_payget', $id)->where('ip.status', '1')->where('mip.status', '1')->orderBy('mip.sort', 'ASC')->get();

        return $instructions;
    }
}
