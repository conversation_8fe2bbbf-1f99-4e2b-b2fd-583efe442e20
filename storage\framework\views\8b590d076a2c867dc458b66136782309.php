<!DOCTYPE html>
<html class="dark" lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo $__env->yieldContent('title', 'KREEN VIP - Exclusive Nightlife Experience'); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css">
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <link rel="shortcut icon" href="<?php echo e(asset('image/favicon.ico')); ?>" type="image/x-icon">


    <!-- Custom KREEN Styles -->
    <link href="<?php echo e(asset('style/main.css')); ?>" rel="stylesheet">

    <!-- Sweet Alert -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="bg-kreen-dark text-white font-inter min-h-screen flex flex-col">
    <?php echo $__env->make('vip.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <main class="flex-1">
        <?php echo $__env->yieldContent('content'); ?>
    </main>


    <!-- Scripts -->
    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    </script>

    <script src="<?php echo e(asset('libs/jquery-3.7.1.min.js')); ?>"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        function formatCurrency(nominal, currency, onlyFormat = false) {
        if (!onlyFormat) {
            nominal = parseFloat(nominal).toFixed(5);
            if (currency === 'IDR') {
                nominal = Math.ceil(nominal).toFixed(0);
            } else {
                nominal = (Math.ceil(nominal * 100) / 100).toFixed(2);
            }
        } else {
            if (currency === 'IDR') {
                nominal = nominal.toFixed(0);
            } else {
                nominal = nominal.toFixed(2);
            }
        }
        return {
            result: currency + ' ' + nominal.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
            number: parseFloat(nominal)
        }
    }
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\laragon\www\vipkreen\kreenvip-new\resources\views/vip/app.blade.php ENDPATH**/ ?>