<?php

namespace App\Http\Controllers;

use App\Helpers\ApiHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class EventController extends Controller
{
    public function exploreEvents()
    {
        $event_cities = DB::select("SELECT DISTINCT e.id_kota as id, ar.name
        FROM tbl_bar_event as e
        JOIN kreen_production_online.tbl_adm_regency as ar ON e.id_kota  = ar.id
        WHERE e.flag_aktif = '1' AND e.tanggal >= DATE(NOW())
        ORDER BY ar.name");
        return view('vip.event.explore_events', compact('event_cities'));
    }

    public function detailEvent($slug)
    {
        $event = DB::table('tbl_bar_event as e')
            ->join('tbl_bar_venue as v', 'e.id_venue', '=', 'v.id')
            ->select('e.*', 'v.nama as venue_name', 'v.alamat as venue_address', 'v.lat as venue_lat', 'v.lng as venue_lng')
            ->where('e.slug', $slug)->first();
        $djs = DB::table('tbl_piv_event_dj as pd')
            ->join('tbl_bar_dj as dj', 'pd.id_dj', '=', 'dj.id')
            ->select('dj.*')
            ->where('pd.id_event', $event->id)->get();
        return view('vip.event.detail_events', compact('event', 'djs'));
    }
}
