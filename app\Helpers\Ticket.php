<?php

namespace App\Helpers;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class Ticket
{
    public static function getTicketsByIdAndDate(array $ticketIds, string $date = null): array
    {
        if (empty($ticketIds)) {
            return [];
        }

        if ($date == null) {
            $date = now()->format('Y-m-d');
        }

        $dateIndex = (int) date('N', strtotime($date)); // 1 (Senin) - 7 (Minggu)

        $placeholders = implode(',', array_fill(0, count($ticketIds), '?'));

        $bindings = array_merge([$date, $dateIndex], $ticketIds);

        return DB::select("
            SELECT
                t.id,
                t.id_venue,
                t.nama_tiket,
                t.stok_tersedia,
                COALESCE(th.harga, th2.harga, t.default_harga) as harga,
                COALESCE(th.stok, th2.stok, t.default_stok) as stok
            FROM tbl_bar_jenis_tiket t
            LEFT JOIN tbl_bar_jenis_tiket_harga th
                ON t.id = th.id_jenis_tiket
                AND th.tanggal = ?
                AND th.hari IS NULL
            LEFT JOIN tbl_bar_jenis_tiket_harga th2
                ON t.id = th2.id_jenis_tiket
                AND th2.tanggal IS NULL
                AND th2.hari = ?
            WHERE t.id IN ($placeholders)
        ", $bindings);
    }

    public static function getTicketsByVenueIdAndDate(string $venue_id, string $date = null): array
    {
        if ($date == null) {
            $date = now()->format('Y-m-d');
        }

        $dateIndex = (int) date('N', strtotime($date)); // 1 (Senin) - 7 (Minggu)

        return DB::select("SELECT
    t.id,
    t.id_venue,
    t.nama_tiket,
    t.stok_tersedia,
    COALESCE(h.harga, t.default_harga) AS harga,
    COALESCE(h.stok, t.default_stok) AS stok,
    COALESCE(h.disc_persen, 0) AS disc_persen,
    COALESCE(h.disc_flat, 0) AS disc_flat
FROM tbl_bar_jenis_tiket t
LEFT JOIN (
    SELECT * FROM tbl_bar_jenis_tiket_harga
    WHERE (tanggal = ? AND hari IS NULL)
       OR (tanggal IS NULL AND hari = ?)
    ORDER BY
        CASE
            WHEN tanggal IS NOT NULL THEN 1
            ELSE 2
        END
) h ON t.id = h.id_jenis_tiket
WHERE t.id_venue = ? AND t.flag_aktif = '1'
ORDER BY harga ASC, stok ASC
", [$date, $dateIndex, $venue_id]);
    }

    public static function getOrderedTicketsByOrderId(string $id_order): Collection
    {
        $ordered_tickets = DB::table('tbl_bar_order_detail as od')
            ->selectRaw('od.id_jenis_tiket as id, od.qty as quantity, od.price as harga, od.qty * od.price as total_harga, jt.nama_tiket')
            ->join('tbl_bar_jenis_tiket as jt', DB::raw("od.id_jenis_tiket "), 'jt.id')
            ->where('od.id_order', $id_order)->get();

        $tickets = $ordered_tickets->groupBy('id')->map(fn ($group) => [
            'id' => $group->first()->id,
            'nama_tiket' => $group->first()->nama_tiket,
            'qty' => $group->sum('quantity'),
            'harga_satuan' => $group->first()->harga,
            'total_harga' => $group->sum('total_harga'),
        ]);

        return $tickets;
    }

    public static function getAvailableStock(string $id_ticket, string $date = null): int
    {
        if ($date == null) {
            $date = now()->format('Y-m-d');
        }

        $dateIndex = (int) date('N', strtotime($date)); // 1 (Senin) - 7 (Minggu)

        $ticket = DB::select("
SELECT
    t.id,
    t.id_venue,
    t.nama_tiket,
    COALESCE(h.harga, t.default_harga, 0) AS harga,
    COALESCE(h.stok, t.default_stok, 0) AS stok,
    COALESCE(h.disc_persen, 0) AS disc_persen,
    COALESCE(h.disc_flat, 0) AS disc_flat,
    (COALESCE(h.stok, t.default_stok, 0) - IFNULL(s.total_terjual, 0)) AS stok_tersedia
FROM tbl_bar_jenis_tiket t
LEFT JOIN (
    SELECT *
    FROM tbl_bar_jenis_tiket_harga
    WHERE (tanggal = ? AND hari IS NULL) OR (tanggal IS NULL AND hari = ?)
    ORDER BY tanggal DESC
) h ON h.id_jenis_tiket = t.id

LEFT JOIN (
    SELECT od.id_jenis_tiket, SUM(od.qty) AS total_terjual
    FROM tbl_bar_order_detail od
    INNER JOIN tbl_bar_order o ON od.id_order = o.id
    WHERE o.status IN ('1', '3')
      AND DATE(o.created_at) = CURDATE()
    GROUP BY od.id_jenis_tiket
) s ON s.id_jenis_tiket = t.id

WHERE t.id = ? AND t.flag_aktif = '1'
LIMIT 1
", [$date, $dateIndex, $id_ticket]);

        return $ticket[0]->stok_tersedia;
    }
}
