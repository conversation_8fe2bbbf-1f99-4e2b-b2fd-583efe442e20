<?php

namespace App\Helpers;

use Exception;
use App\Mail\SendEticketPerEmail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class Email
{
    public static function sendEticketPerEmail($id_order)
    {
        try {
            $orders = DB::select('SELECT od.*, o.status status_bayar, t.nama_tiket, v.id id_venue, v.nama nama_venue, o.date, v.alamat FROM tbl_bar_order_detail od
            JOIN tbl_bar_order o ON od.id_order = o.id
            JOIN tbl_bar_jenis_tiket t ON od.id_jenis_tiket = t.id
            JOIN tbl_bar_venue v ON o.id_venue = v.id
            WHERE o.id = :id ORDER BY od.id', [
                'id' => $id_order,
            ]);
            $data = [];
            foreach ($orders as $item) {
                $data[$item->email]['ticket'][$item->id_jenis_tiket]['name_ticket'] = $item->nama_tiket;
                $data[$item->email]['order_detail'][$item->id_jenis_tiket][$item->id]['fullname'] = trim($item->full_name);
                $data[$item->email]['order_detail'][$item->id_jenis_tiket][$item->id]['email'] = $item->email;
                $data[$item->email]['order']['id_order'] = $item->id_order;
                $data[$item->email]['event']['nama_venue'] = $item->nama_venue;
                $data[$item->email]['event']['alamat'] = $item->alamat;
                $data[$item->email]['order']['date'] = $item->date;
            }
            foreach ($data as $key => $item) {
                Mail::to($key)->send(new SendEticketPerEmail($data[$key]));
            }
            return true;
        } catch (Exception $e) {
            Log::error("Error send email eticket per email: " . $e->getMessage());
            return false;
        }
    }
}
