<?php

namespace App\Http\Controllers;

use App\Helpers\ApiHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DjController extends Controller
{
    public function profileDj($slug)
    {
        $dj = DB::table('tbl_bar_dj')->where('slug', $slug)->first();
        $galleries = DB::table('tbl_bar_dj_galeri')->where('id_dj', $dj->id)->get();
        $upcomingEvents = DB::table('tbl_piv_event_dj as pd')
            ->join('tbl_bar_event as e', 'pd.id_event', '=', 'e.id')
            ->join('tbl_bar_venue as v', 'e.id_venue', '=', 'v.id')
            ->where('pd.id_dj', $dj->id)
            ->select('e.id', 'e.nama', 'e.tanggal', 'e.slug', 'v.nama as venue_name')
            ->where('e.tanggal', '>=', date('Y-m-d'))
            ->limit(2)
            ->get();
        $pastEvents = DB::table('tbl_piv_event_dj as pd')
            ->join('tbl_bar_event as e', 'pd.id_event', '=', 'e.id')
            ->join('tbl_bar_venue as v', 'e.id_venue', '=', 'v.id')
            ->where('pd.id_dj', $dj->id)
            ->select('e.id', 'e.nama', 'e.tanggal', 'e.slug', 'v.nama as venue_name')
            ->where('e.tanggal', '<', date('Y-m-d'))
            ->get();
        return view('vip.dj.profile', compact('dj', 'galleries', 'upcomingEvents', 'pastEvents'));
    }

    public function listDj()
    {
        $dj_origins = DB::select("SELECT DISTINCT v.origin
        FROM tbl_bar_dj as v
        WHERE v.flag_aktif = '1'
        ORDER BY v.origin");
        return view('vip.dj.list', compact('dj_origins'));
    }

    public function ajaxListDj()
    {
        $gender = request()->gender;
        $origin = request()->origin;
        $page = (int)request()->page;
        $limit = request()->limit ?? 12;
        $offset = ($page - 1) * $limit;
        $query = DB::table('tbl_bar_dj')->where('flag_aktif', '1');
        if ($gender) {
            $query->where('gender', $gender);
        }
        if ($origin) {
            $query->where('origin', $origin);
        }
        $djs = $query->orderBy('nama', 'asc')->offset($offset)->limit($limit)->get();
        $count_dj = $query->count();
        $has_more = $count_dj > $page * $limit;

        $view = view('vip.dj.ajax.card-dj', compact('djs'))->render();
        return response()->json(['view' => $view, 'has_more' => $has_more]);
    }

}
