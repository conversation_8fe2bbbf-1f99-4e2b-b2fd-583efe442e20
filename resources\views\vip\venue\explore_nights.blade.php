@extends('vip.app')

@section('title', 'Explore Nights - KREEN VIP')

@push('styles')
    <style>
        select.custom-select option {
            background-color: black;
            color: white;
        }

        select.custom-select option:hover {
            background-color: #1f2937; /* Tailwind slate-800 */
        }
    </style>
@endpush

@section('content')
    <!-- Explore Nights Page Background Container -->
    <div class="explore-nights-bg-container">
        <!-- Main Content -->
        <section class="explore-nights-content py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Page Header -->
                <div class="text-center mb-12">
                    <h1 class="text-4xl md:text-5xl font-bold text-white mb-8">Explore Nights</h1>

                    <!-- Location Dropdown -->
                    <div class="mb-12">
                        <div class="relative inline-block">
                            <select
                                class="bg-transparent border border-gray-600 text-white px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-kreen-gold appearance-none pr-8 custom-select"
                                onchange="handleCityChange()" data-type="night" id="night-city">
                                <option value="" class="text-black dark:text-white">Filter City</option>
                                @foreach ($venue_cities as $venue_city)
                                    <option class="text-black dark:text-white" value="{{ $venue_city->id }}">{{ $venue_city->name }}</option>
                                @endforeach
                            </select>
                            <svg class="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white pointer-events-none"
                                fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Venues Grid -->
                <div id="venues-data-from-ajax"></div>

                <!-- Load More Button -->
                <div class="text-center mt-12">
                    <button id="load-more-btn"
                        class="bg-kreen-gold hover:bg-kreen-gold-hover text-black font-bold px-8 py-3 rounded-lg transition-all duration-300 transform hover:scale-105">
                        Load More Venues
                    </button>
                </div>
            </div>
        </section>
    </div>
@endsection

@push('scripts')
    <script>
        const loadMoreBtn = document.getElementById('load-more-btn');
        let currentPage = 1;

        document.addEventListener('DOMContentLoaded', function () {
            // Load more button
            loadMoreBtn.addEventListener('click', function () {
                console.log('Load more venues clicked');
                currentPage++;
                loadVenues(true);
            });

            // Sticky Navbar Scroll Effect
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                window.addEventListener('scroll', function () {
                    if (window.scrollY > 50) {
                        navbar.classList.add('scrolled');
                    } else {
                        navbar.classList.remove('scrolled');
                    }
                });
            }

            // Initial Load
            loadVenues();
        });

        function loadVenues(isLoadMore = false) {

            const selectedCityId = document.querySelector('select[data-type="night"]').value;

            if (!isLoadMore) {
                currentPage = 1;
            }

            $.ajax({
                url: '/ajax/explore_nights',
                type: 'GET',
                dataType: 'json',
                data: {
                    id_kota: selectedCityId,
                    page: currentPage,
                    limit: 8
                },
                success: function (response) {
                    const venuesGrid = document.getElementById('venues-data-from-ajax');
                    console.log(response);

                        if (isLoadMore) {
                            venuesGrid.innerHTML += response?.view;
                        } else {
                            venuesGrid.innerHTML = response?.view;
                        }

                    // Show/hide load more button
                    if (response.has_more) {
                        loadMoreBtn.style.display = 'inline-block';

                    } else {
                        loadMoreBtn.style.display = 'none';
                    }
                },
                error: function (xhr, status, error) {
                    console.error('AJAX Error:', error);
                }
            });
        }

        function handleCityChange() {
            currentPage = 1;
            loadVenues(false);
        }
    </script>
@endpush
