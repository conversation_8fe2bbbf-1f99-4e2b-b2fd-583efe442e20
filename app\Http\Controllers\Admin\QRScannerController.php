<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class QRScannerController extends Controller
{
    public function index()
    {

        $user = Auth::user();
        $merchantId = $user->id_merchant ?? null;

        // dd($merchantId);

        // For testing: use a default merchant ID if not available
        if (!$merchantId) {
            // $merchantId = '1'; // Default merchant ID for testing
            abort(404);

        }

        return view('admin.qr-scanner');
    }

    public function scan(Request $request)
    {
        try {
            $request->validate([
                'qr_code' => 'required|string'
            ]);

            $qrCode = $request->qr_code;

            // Get merchant ID from authenticated user
            $user = Auth::user();
            $merchantId = $user->id_merchant ?? null;

            // For testing: use a default merchant ID if not available
            if (!$merchantId) {
                $merchantId = '1'; // Default merchant ID for testing
            }

            // Debug log
            Log::info("QR Scanner attempt", [
                'qr_code' => $qrCode,
                'merchant_id' => $merchantId,
                'user_id' => $user->id ?? 'unknown'
            ]);

            // Find booking detail by QR code (id from tbl_bar_order_detail) and ensure it belongs to merchant's venue
            // QR code contains the ID from tbl_bar_order_detail
            $bookingDetail = DB::table('tbl_bar_order_detail as od')
                ->join('tbl_bar_order as o', 'od.id_order', '=', 'o.id')
                ->join('tbl_bar_venue as v', 'o.id_venue', '=', 'v.id')
                ->where('od.id', $qrCode)  // QR code is the ID from tbl_bar_order_detail
                ->where('v.id_merchant', $merchantId)
                ->select('od.*', 'o.date', 'o.total_amount', 'o.phone', 'v.nama as venue_name')
                ->first();

            if (!$bookingDetail) {
                // Debug: Check if order detail exists
                $orderDetailExists = DB::table('tbl_bar_order_detail')->where('id', $qrCode)->first();
                $orderExists = null;
                if ($orderDetailExists) {
                    $orderExists = DB::table('tbl_bar_order')->where('id', $orderDetailExists->id_order)->first();
                }

                Log::info("QR Code not found", [
                    'qr_code' => $qrCode,
                    'merchant_id' => $merchantId,
                    'order_detail_exists' => $orderDetailExists ? 'yes' : 'no',
                    'order_exists' => $orderExists ? 'yes' : 'no',
                    'order_detail_data' => $orderDetailExists
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'QR Code tidak ditemukan atau tidak valid untuk merchant ini.',
                    'debug' => [
                        'qr_code' => $qrCode,
                        'merchant_id' => $merchantId,
                        'order_detail_exists' => $orderDetailExists ? true : false,
                        'order_exists' => $orderExists ? true : false
                    ]
                ], 404);
            }

            // Check if already scanned
            if ($bookingDetail->flag_scanned == '1') {
                return response()->json([
                    'success' => false,
                    'message' => 'QR Code ini sudah pernah di-scan sebelumnya.',
                    'booking' => [
                        'nama_pengunjung' => $bookingDetail->full_name,
                        'venue_name' => $bookingDetail->venue_name,
                        'tanggal_kunjungan' => $bookingDetail->date ? date('l, d F Y', strtotime($bookingDetail->date)) : '-',
                        'status' => 'Sudah Scan'
                    ]
                ], 400);
            }

            // Update flag_scanned to 1 in order detail
            DB::table('tbl_bar_order_detail')
                ->where('id', $bookingDetail->id)
                ->update([
                    'flag_scanned' => '1',
                    'updated_at' => now()
                ]);

            Log::info("QR Code scanned successfully", [
                'booking_detail_id' => $bookingDetail->id,
                'order_id' => $bookingDetail->id_order,
                'qr_code' => $qrCode,
                'merchant_id' => $merchantId,
                'scanned_by' => $user->id ?? 'unknown'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'QR Code berhasil di-scan! Pengunjung sudah check-in.',
                'booking' => [
                    'nama_pengunjung' => $bookingDetail->full_name,
                    'venue_name' => $bookingDetail->venue_name,
                    'tanggal_kunjungan' => $bookingDetail->date ? date('l, d F Y', strtotime($bookingDetail->date)) : '-',
                    'nomor_whatsapp' => $bookingDetail->phone ?? '-',
                    'total_amount' => 'Rp ' . number_format($bookingDetail->total_amount ?? 0, 0, ',', '.'),
                    'status' => 'Sudah Scan'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("QR Scanner error", [
                'error' => $e->getMessage(),
                'qr_code' => $request->qr_code ?? 'unknown'
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memproses QR Code. Silakan coba lagi.'
            ], 500);
        }
    }
}
