@foreach ($djs as $dj)
    <!-- DJ 1 -->
    <a href="{{ route('dj.profileDj', $dj->slug) }}" class="dj-card" data-origin="{{ $dj->origin }}" data-gender="{{ $dj->gender }}">
        <div class="dj-image"
            style="background-image: url('{{ strpos($dj->gambar, 'http') === 0 ? $dj->gambar : asset($dj->gambar) }}');">
        </div>
        <div class="dj-info">
            <h3 class="dj-name">{{ $dj->nama }}</h3>
            <div class="dj-origin">
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                </svg>
                {{ $dj->origin }}
            </div>
        </div>
    </a>
@endforeach
