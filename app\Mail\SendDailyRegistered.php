<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SendDailyRegistered extends Mailable
{
    use Queueable, SerializesModels;
    public $count;
    public $perDay;
    public $count_yesterday;
    public $email;
    public $eventRegister;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($count, $perDay, $count_yesterday, $email, $eventRegister)
    {
        $this->count = $count;
        $this->count_yesterday = $count_yesterday;
        $this->perDay = $perDay;
        $this->email = $email;
        $this->eventRegister = $eventRegister;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.registeredcount')
                    ->subject("Daily Report User Growth");;
    }
}
