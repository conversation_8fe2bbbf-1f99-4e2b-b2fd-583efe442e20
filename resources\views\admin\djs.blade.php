@extends('layouts.admin')

@section('title', 'DJs - Admin Panel')
@section('page-title', 'DJs')

@section('content')
<div class="space-y-4 sm:space-y-6">
    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-3 sm:gap-4">
        <button id="add-dj-btn" class="flex items-center justify-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium text-sm">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Add New DJ</span>
        </button>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-4 sm:p-6">
        <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <!-- Status Filter -->
            <div class="flex items-center space-x-2">
                <select id="status-filter" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                    <option value="">All Status</option>
                    <option value="1">Active</option>
                    <option value="0">Inactive</option>
                </select>
            </div>

            <!-- Search -->
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <input type="text" id="search-input" placeholder="Search DJs..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                    <svg class="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Table -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 border-b border-gray-200">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">DJ Name</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Origin</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody id="dj-table-body" class="bg-white divide-y divide-gray-200">
                    <!-- DJ rows will be loaded here -->
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 sm:px-6">
            <div class="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
                <div id="pagination-info" class="text-sm text-gray-700">
                    <!-- Pagination info will be shown here -->
                </div>
                <div id="load-more-container" class="hidden">
                    <button type="button" class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors font-medium text-sm" id="load-more-btn">
                        Load More
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit DJ Modal -->
<div id="dj-modal" class="fixed inset-0 bg-black/30 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-6xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center mb-4">
                <h3 id="modal-title" class="text-lg font-medium text-gray-900">Add New DJ</h3>
                <button id="close-dj-modal" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form id="dj-form" enctype="multipart/form-data" class="mt-6">
                <input type="hidden" id="dj-id" name="dj_id" value="">

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="space-y-4">
                        <h4 class="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">Basic Information</h4>

                        <div>
                            <label for="nama" class="block text-sm font-medium text-gray-700 mb-1">DJ Name *</label>
                            <input type="text" id="nama" name="nama" required class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                        </div>

                        <div>
                            <label for="origin" class="block text-sm font-medium text-gray-700 mb-1">Origin</label>
                            <input type="text" id="origin" name="origin" placeholder="e.g., Jakarta, Indonesia" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                        </div>

                        <div>
                            <label for="gender" class="block text-sm font-medium text-gray-700 mb-1">Gender *</label>
                            <select id="gender" name="gender" required class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                                <option value="">Select Gender</option>
                                <option value="Male">Male</option>
                                <option value="Female">Female</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>

                        <div>
                            <label for="alamat" class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                            <textarea id="alamat" name="alamat" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"></textarea>
                        </div>

                        <div>
                            <label for="deskripsi" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <textarea id="deskripsi" name="deskripsi" rows="4" placeholder="Tell us about this DJ..." class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"></textarea>
                        </div>

                        <div>
                            <label for="flag_aktif" class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
                            <select id="flag_aktif" name="flag_aktif" required class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                                <option value="1">Active</option>
                                <option value="0">Inactive</option>
                            </select>
                        </div>
                    </div>

                    <!-- Social Media & Images -->
                    <div class="space-y-4">
                        <h4 class="text-md font-medium text-gray-900 border-b border-gray-200 pb-2">Social Media Links</h4>

                        <div>
                            <label for="spotify" class="block text-sm font-medium text-gray-700 mb-1">Spotify URL</label>
                            <input type="url" id="spotify" name="spotify" placeholder="https://open.spotify.com/artist/..." class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                        </div>

                        <div>
                            <label for="fb" class="block text-sm font-medium text-gray-700 mb-1">Facebook URL</label>
                            <input type="url" id="fb" name="fb" placeholder="https://facebook.com/..." class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                        </div>

                        <div>
                            <label for="ig" class="block text-sm font-medium text-gray-700 mb-1">Instagram Username</label>
                            <div class="flex">
                                <span class="inline-flex items-center px-3 text-sm text-gray-500 bg-gray-50 border border-r-0 border-gray-300 rounded-l-lg">@</span>
                                <input type="text" id="ig" name="ig" placeholder="username" class="flex-1 px-3 py-2 border border-gray-300 rounded-r-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                            </div>
                        </div>

                        <div>
                            <label for="yt" class="block text-sm font-medium text-gray-700 mb-1">YouTube URL</label>
                            <input type="url" id="yt" name="yt" placeholder="https://youtube.com/..." class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                        </div>

                        <div>
                            <label for="yt_music" class="block text-sm font-medium text-gray-700 mb-1">YouTube Music URL</label>
                            <input type="url" id="yt_music" name="yt_music" placeholder="https://music.youtube.com/..." class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                        </div>

                        <div>
                            <label for="soundcloud" class="block text-sm font-medium text-gray-700 mb-1">SoundCloud URL</label>
                            <input type="url" id="soundcloud" name="soundcloud" placeholder="https://soundcloud.com/..." class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                        </div>

                        <h4 class="text-md font-medium text-gray-900 border-b border-gray-200 pb-2 mt-6">Images</h4>

                        <div>
                            <label for="gambar" class="block text-sm font-medium text-gray-700 mb-1">Main Image</label>
                            <input type="file" id="gambar" name="gambar" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                            <p class="text-xs text-gray-500 mt-1">Upload main DJ image (max 2MB)</p>
                        </div>

                        <div>
                            <label for="galleries" class="block text-sm font-medium text-gray-700 mb-1">Gallery Images</label>
                            <input type="file" id="galleries" name="galleries[]" accept="image/*" multiple class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                            <p class="text-xs text-gray-500 mt-1">Upload multiple gallery images (max 2MB each)</p>
                        </div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                    <button type="button" id="cancel-dj-btn" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                        Cancel
                    </button>
                    <button type="submit" id="save-dj-btn" class="px-4 py-2 text-sm font-medium text-white bg-yellow-600 border border-transparent rounded-lg hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                        Save DJ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Detail DJ Modal -->
<div id="detail-dj-modal" class="fixed inset-0 bg-black/30 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">DJ Details</h3>
                <button id="close-detail-modal" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Content -->
            <div id="detail-content">
                <!-- DJ details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Custom Alert Modal -->
<div id="custom-alert" class="fixed inset-0 bg-black/30 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <!-- Alert Icon -->
            <div id="alert-icon" class="mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4">
                <!-- Success Icon -->
                <svg id="success-icon" class="w-6 h-6 text-green-600 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <!-- Error Icon -->
                <svg id="error-icon" class="w-6 h-6 text-red-600 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                <!-- Warning Icon -->
                <svg id="warning-icon" class="w-6 h-6 text-yellow-600 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <!-- Info Icon -->
                <svg id="info-icon" class="w-6 h-6 text-blue-600 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>

            <!-- Alert Title -->
            <h3 id="alert-title" class="text-lg font-medium text-gray-900 mb-2"></h3>

            <!-- Alert Message -->
            <p id="alert-message" class="text-sm text-gray-500 mb-4"></p>

            <!-- Alert Buttons -->
            <div id="alert-buttons" class="flex justify-center space-x-3">
                <button id="alert-ok-btn" class="px-4 py-2 bg-yellow-600 text-white text-sm font-medium rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                    OK
                </button>
                <button id="alert-cancel-btn" class="px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 hidden">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const addDjBtn = document.getElementById('add-dj-btn');
    const djModal = document.getElementById('dj-modal');
    const closeDjModal = document.getElementById('close-dj-modal');
    const cancelDjBtn = document.getElementById('cancel-dj-btn');
    const djForm = document.getElementById('dj-form');
    const modalTitle = document.getElementById('modal-title');
    const saveDjBtn = document.getElementById('save-dj-btn');

    // Detail modal elements
    const detailModal = document.getElementById('detail-dj-modal');
    const closeDetailModal = document.getElementById('close-detail-modal');
    const detailContent = document.getElementById('detail-content');

    const searchInput = document.getElementById('search-input');
    const statusFilter = document.getElementById('status-filter');
    const djTableBody = document.getElementById('dj-table-body');
    const loadMoreBtn = document.getElementById('load-more-btn');
    const loadMoreContainer = document.getElementById('load-more-container');
    const paginationInfo = document.getElementById('pagination-info');

    // State
    let currentPage = 1;
    let hasMore = true;
    let isLoading = false;

    // Custom Alert Functions
    class CustomAlert {
        constructor() {
            this.modal = document.getElementById('custom-alert');
            this.title = document.getElementById('alert-title');
            this.message = document.getElementById('alert-message');
            this.iconContainer = document.getElementById('alert-icon');
            this.okBtn = document.getElementById('alert-ok-btn');
            this.cancelBtn = document.getElementById('alert-cancel-btn');
            this.alertButtons = document.getElementById('alert-buttons');

            // Bind events
            this.okBtn.addEventListener('click', () => this.hide());
            this.cancelBtn.addEventListener('click', () => this.hide());
            this.modal.addEventListener('click', (e) => {
                if (e.target === this.modal) this.hide();
            });
        }

        show(type, title, message, options = {}) {
            return new Promise((resolve) => {
                this.title.textContent = title;
                this.message.textContent = message;

                // Reset icons
                document.querySelectorAll('#alert-icon svg').forEach(icon => icon.classList.add('hidden'));

                // Set icon and colors based on type
                const iconColors = {
                    success: 'bg-green-100',
                    error: 'bg-red-100',
                    warning: 'bg-yellow-100',
                    info: 'bg-blue-100'
                };

                this.iconContainer.className = `mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4 ${iconColors[type]}`;
                document.getElementById(`${type}-icon`).classList.remove('hidden');

                // Handle buttons
                if (options.showCancel) {
                    this.cancelBtn.classList.remove('hidden');
                    this.cancelBtn.textContent = options.cancelText || 'Cancel';
                } else {
                    this.cancelBtn.classList.add('hidden');
                }

                this.okBtn.textContent = options.confirmText || 'OK';

                // Show modal
                this.modal.classList.remove('hidden');

                // Handle resolve
                this.resolvePromise = resolve;
                this.okBtn.onclick = () => {
                    this.hide();
                    resolve(true);
                };
                this.cancelBtn.onclick = () => {
                    this.hide();
                    resolve(false);
                };
            });
        }

        hide() {
            this.modal.classList.add('hidden');
            if (this.resolvePromise) {
                this.resolvePromise(false);
            }
        }

        success(title, message) {
            return this.show('success', title, message);
        }

        error(title, message) {
            return this.show('error', title, message);
        }

        warning(title, message) {
            return this.show('warning', title, message);
        }

        info(title, message) {
            return this.show('info', title, message);
        }

        confirm(title, message, options = {}) {
            return this.show('warning', title, message, {
                showCancel: true,
                confirmText: options.confirmText || 'Confirm',
                cancelText: options.cancelText || 'Cancel'
            });
        }
    }

    // Initialize custom alert
    const customAlert = new CustomAlert();

    // Load DJs
    function loadDjs(append = false) {
        if (isLoading) return;
        isLoading = true;

        const search = searchInput.value;
        const status = statusFilter.value;

        if (!append) {
            currentPage = 1;
            djTableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="px-4 py-8 text-center">
                        <div class="flex justify-center">
                            <svg class="animate-spin h-8 w-8 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">Loading DJs...</p>
                    </td>
                </tr>
            `;
        }

        fetch(`/admin/djs/data?page=${currentPage}&search=${search}&status=${status}&limit=10`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.djs && data.djs.length > 0) {
                    let html = '';
                    data.djs.forEach(dj => {
                        html += createDjRow(dj);
                    });

                    if (append) {
                        djTableBody.insertAdjacentHTML('beforeend', html);
                    } else {
                        djTableBody.innerHTML = html;
                    }

                    // Update pagination info
                    const pagination = data.pagination;
                    hasMore = pagination.has_more;

                    paginationInfo.innerHTML = `Showing ${pagination.showing_from}-${pagination.showing_to} of ${pagination.total_count} results`;

                    // Show/hide load more button
                    if (hasMore) {
                        loadMoreContainer.classList.remove('hidden');
                    } else {
                        loadMoreContainer.classList.add('hidden');
                    }
                } else {
                    if (!append) {
                        djTableBody.innerHTML = `
                            <tr>
                                <td colspan="7" class="px-4 py-8 text-center">
                                    <div class="flex justify-center">
                                        <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <p class="mt-2 text-sm text-gray-500">No DJs found</p>
                                    <button onclick="showModal()" class="mt-3 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-full shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                                        Add Your First DJ
                                    </button>
                                </td>
                            </tr>
                        `;
                    }
                    loadMoreContainer.classList.add('hidden');
                }
            } else {
                customAlert.error('Error', data.message || 'Failed to load DJs');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            customAlert.error('Error', 'An error occurred while loading DJs');
        })
        .finally(() => {
            isLoading = false;
        });
    }

    // Create DJ row HTML
    function createDjRow(dj) {
        const statusBadge = dj.flag_aktif == '1'
            ? '<span class="inline-flex px-2 py-1 text-xs font-semibold text-green-800 bg-green-100 rounded-full">Active</span>'
            : '<span class="inline-flex px-2 py-1 text-xs font-semibold text-red-800 bg-red-100 rounded-full">Inactive</span>';

        const image = dj.gambar
            ? `<img src="${dj.gambar}" alt="${dj.nama}" class="w-12 h-12 rounded-lg object-cover">`
            : '<div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center"><svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path></svg></div>';

        const createdAt = new Date(dj.created_at).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });

        return `
            <tr class="hover:bg-gray-50">
                <td class="px-4 py-4 whitespace-nowrap">
                    ${image}
                </td>
                <td class="px-4 py-4">
                    <div class="text-sm font-medium text-gray-900">${dj.nama}</div>
                    <div class="text-sm text-gray-500">${dj.slug || ''}</div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">${dj.origin || '-'}</div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">${dj.gender || '-'}</div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    ${statusBadge}
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${createdAt}
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                        <button type="button" onclick="viewDj(${dj.id})" class="text-blue-600 hover:text-blue-900" title="View Details">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                        <button type="button" onclick="editDj(${dj.id})" class="text-yellow-600 hover:text-yellow-900" title="Edit">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                            </svg>
                        </button>
                        
                    </div>
                </td>
            </tr>
        `;
    }

    // Reset form
    function resetForm() {
        djForm.reset();
        document.getElementById('dj-id').value = '';
        modalTitle.textContent = 'Add New DJ';
        saveDjBtn.textContent = 'Save DJ';
    }

    // Modal functions
    function showModal() {
        djModal.classList.remove('hidden');
    }

    function hideModal() {
        djModal.classList.add('hidden');
    }

    function showDetailModal() {
        detailModal.classList.remove('hidden');
    }

    function hideDetailModal() {
        detailModal.classList.add('hidden');
    }

    // Event listeners
    addDjBtn.addEventListener('click', function() {
        resetForm();
        showModal();
    });

    closeDjModal.addEventListener('click', hideModal);
    cancelDjBtn.addEventListener('click', hideModal);
    closeDetailModal.addEventListener('click', hideDetailModal);

    // Close modal when clicking outside
    djModal.addEventListener('click', function(e) {
        if (e.target === djModal) {
            hideModal();
        }
    });

    detailModal.addEventListener('click', function(e) {
        if (e.target === detailModal) {
            hideDetailModal();
        }
    });

    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loadDjs();
        }
    });

    searchInput.addEventListener('input', function() {
        // Debounce search
        clearTimeout(window.searchTimeout);
        window.searchTimeout = setTimeout(() => {
            loadDjs();
        }, 500);
    });

    statusFilter.addEventListener('change', function() {
        loadDjs();
    });

    loadMoreBtn.addEventListener('click', function() {
        currentPage++;
        loadDjs(true);
    });

    // Form submission
    djForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(djForm);
        const isEdit = document.getElementById('dj-id').value !== '';
        const djId = document.getElementById('dj-id').value;

        saveDjBtn.disabled = true;
        saveDjBtn.textContent = isEdit ? 'Updating...' : 'Saving...';

        const url = isEdit ? `/admin/djs/${djId}` : '{{ route("admin.djs.store") }}';
        const method = isEdit ? 'PUT' : 'POST';

        // Add method override for PUT requests
        if (isEdit) {
            formData.append('_method', 'PUT');
        }

        // Add CSRF token
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        fetch(url, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                customAlert.success('Success', data.message);
                hideModal();
                loadDjs(); // Reload the list
            } else {
                customAlert.error('Error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            customAlert.error('Error', 'An error occurred while saving the DJ.');
        })
        .finally(() => {
            saveDjBtn.disabled = false;
            saveDjBtn.textContent = isEdit ? 'Update DJ' : 'Save DJ';
        });
    });

    // Global functions for buttons
    window.viewDj = function(djId) {
        fetch(`/admin/djs/${djId}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showDjDetails(data.dj, data.galleries);
            } else {
                customAlert.error('Error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            customAlert.error('Error', 'Failed to load DJ details');
        });
    };

    window.editDj = function(djId) {
        fetch(`/admin/djs/${djId}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                fillEditForm(data.dj);
            } else {
                customAlert.error('Error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            customAlert.error('Error', 'Failed to load DJ data');
        });
    };

    // Removed delete function as per user request

    // Show DJ details in modal
    function showDjDetails(dj, galleries) {


        let socialLinks = '';
        if (dj.spotify || dj.fb || dj.ig || dj.yt || dj.yt_music || dj.soundcloud) {
            socialLinks = `
                <div class="mt-6">
                    <h4 class="text-md font-medium text-gray-900 border-b border-gray-200 pb-2 mb-3">Social Media</h4>
                    <div class="flex flex-wrap gap-2">
                        ${dj.spotify ? `<a href="${dj.spotify}" target="_blank" class="inline-flex items-center px-3 py-1 text-sm font-medium text-green-700 bg-green-100 rounded-full hover:bg-green-200">
                            <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="currentColor"><path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.419 1.56-.299.421-1.02.599-1.559.3z"/></svg>
                            Spotify
                        </a>` : ''}
                        ${dj.fb ? `<a href="${dj.fb}" target="_blank" class="inline-flex items-center px-3 py-1 text-sm font-medium text-blue-700 bg-blue-100 rounded-full hover:bg-blue-200">
                            <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>
                            Facebook
                        </a>` : ''}
                        ${dj.ig ? `<a href="https://instagram.com/${dj.ig}" target="_blank" class="inline-flex items-center px-3 py-1 text-sm font-medium text-pink-700 bg-pink-100 rounded-full hover:bg-pink-200">
                            <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z"/></svg>
                            Instagram
                        </a>` : ''}
                        ${dj.yt ? `<a href="${dj.yt}" target="_blank" class="inline-flex items-center px-3 py-1 text-sm font-medium text-red-700 bg-red-100 rounded-full hover:bg-red-200">
                            <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="currentColor"><path d="M23.498 6.186a3.016 3.016 0 00-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 00.502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 002.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 002.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/></svg>
                            YouTube
                        </a>` : ''}
                        ${dj.yt_music ? `<a href="${dj.yt_music}" target="_blank" class="inline-flex items-center px-3 py-1 text-sm font-medium text-red-700 bg-red-100 rounded-full hover:bg-red-200">
                            <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="currentColor"><path d="M12 0C5.376 0 0 5.376 0 12s5.376 12 12 12 12-5.376 12-12S18.624 0 12 0zm0 19.104c-3.924 0-7.104-3.18-7.104-7.104S8.076 4.896 12 4.896s7.104 3.18 7.104 7.104-3.18 7.104-7.104 7.104zm0-13.332c-3.432 0-6.228 2.796-6.228 6.228S8.568 18.228 12 18.228s6.228-2.796 6.228-6.228S15.432 5.772 12 5.772zM9.684 15.54V8.46L16.2 12l-6.516 3.54z"/></svg>
                            YouTube Music
                        </a>` : ''}
                        ${dj.soundcloud ? `<a href="${dj.soundcloud}" target="_blank" class="inline-flex items-center px-3 py-1 text-sm font-medium text-orange-700 bg-orange-100 rounded-full hover:bg-orange-200">
                            <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="currentColor"><path d="M1.175 12.225c-.051 0-.094.046-.101.1l-.233 2.154.233 2.105c.007.058.05.098.101.098.05 0 .09-.04.099-.098l.255-2.105-.27-2.154c0-.057-.045-.1-.09-.1m-.899.828c-.06 0-.091.037-.104.094L0 14.479l.165 1.308c0 .055.045.094.09.094s.089-.045.104-.104l.21-1.319-.21-1.334c0-.061-.044-.09-.09-.09m1.83-1.229c-.061 0-.12.045-.12.104l-.21 2.563.225 2.458c0 .06.045.12.119.12.061 0 .105-.061.121-.12l.254-2.474-.254-2.548c-.016-.06-.061-.12-.121-.12m.945-.089c-.075 0-.135.06-.15.135l-.193 2.64.21 2.544c.016.075.075.135.149.135.061 0 .135-.06.15-.135l.24-2.544-.24-2.64c-.015-.06-.075-.135-.149-.135zm1.155.36c-.005-.09-.075-.149-.159-.149-.09 0-.158.06-.164.149l-.217 2.43.2 2.563c.005.09.075.157.159.157.074 0 .158-.061.158-.151l.227-2.563-.227-2.444.026.008zm.824-.598c-.101 0-.18.09-.18.181l-.21 2.834.21 2.544c0 .09.079.18.18.18.094 0 .174-.09.18-.18l.24-2.544-.24-2.834c0-.09-.08-.18-.18-.18zm.945 0c-.11 0-.203.09-.203.203l-.194 2.801.194 2.525c.016.135.105.226.203.226.104 0 .194-.09.21-.225l.227-2.525-.227-2.801c-.016-.12-.105-.203-.21-.203zm.989.008c-.12 0-.224.089-.224.217l-.182 2.786.182 2.544c0 .128.104.217.224.217s.21-.089.226-.217l.21-2.544-.21-2.786c-.016-.128-.105-.217-.226-.217zm1.24.107c-.138 0-.244.118-.244.256l-.162 2.643.162 2.517c0 .136.106.254.244.254.135 0 .242-.118.242-.254l.182-2.517-.182-2.643c0-.138-.107-.256-.242-.256zm.749-.137c-.135 0-.244.119-.244.269l-.157 2.911.157 2.492c0 .15.109.254.244.254.134 0 .243-.104.243-.254l.179-2.492-.179-2.911c0-.15-.109-.269-.243-.269zm.742-.56c-.15 0-.272.12-.272.27l-.142 3.512.142 2.429c0 .15.12.27.272.27.149 0 .27-.12.27-.27l.157-2.429-.157-3.512c0-.15-.12-.27-.27-.27zm.984.015c-.008-.002-.017-.002-.025-.002-.167 0-.301.134-.301.304l-.128 3.458.128 2.291c0 .152.129.301.301.301.168 0 .301-.148.301-.301l.142-2.291-.142-3.458c0-.152-.133-.304-.301-.304zm1.307-.052c-.177 0-.317.149-.317.327l-.111 3.489.111 2.284c0 .179.14.328.317.328.179 0 .318-.149.318-.328l.124-2.284-.124-3.489c0-.178-.14-.327-.318-.327zm1.346-.075c-.194 0-.344.15-.344.344l-.098 3.498.098 2.258c0 .193.15.344.344.344.193 0 .345-.15.345-.344l.11-2.258-.11-3.498c0-.193-.15-.344-.345-.344zm1.385.094c-.202 0-.362.165-.362.367l-.091 3.361.091 2.258c0 .202.16.367.362.367.201 0 .361-.165.361-.367l.105-2.258-.105-3.361c0-.202-.16-.367-.361-.367zM11.4 9.98c-.203 0-.361.163-.361.366l-.09 3.47.09 2.223c0 .203.158.366.361.366.21 0 .375-.163.375-.366l.09-2.223-.09-3.47c0-.203-.165-.366-.375-.366zm2.168.434c.21 0 .375-.163.375-.365 0-.203-.165-.367-.375-.367-.203 0-.367.164-.367.367 0 .202.164.365.367.365zm0 .366c-.21 0-.375.163-.375.366l-.075 3.083.075 2.243c0 .202.165.365.375.365.203 0 .361-.163.361-.365l.091-2.243-.091-3.083c0-.203-.158-.366-.361-.366zm5.741 2.243c0 3.083-2.5 5.587-5.574 5.587-1.173 0-2.261-.364-3.159-.987l-7.356.022c-.203.016-.367-.15-.367-.344 0-.202.149-.375.344-.375l.015-.008 3.87-.022c.097-.075.202-.149.307-.225l.075-.061c.877-.665 1.577-1.567 1.996-2.608.06-.15.12-.299.165-.449.225-.631.345-1.316.345-2.021 0-.764-.149-1.496-.419-2.168-.27-.675-.659-1.276-1.154-1.8-.239-.255-.479-.496-.749-.706-.074-.06-.149-.12-.239-.18-.27-.195-.54-.359-.839-.496-.164-.075-.329-.149-.509-.209-.36-.12-.734-.209-1.124-.239-.06 0-.104-.016-.165-.016h-.09c-.044 0-.089 0-.134.016-.36.016-.719.09-1.063.209-.75.27-1.423.706-1.951 1.276-.239.255-.449.54-.644.839-.195.315-.36.63-.479.959-.165.436-.255.886-.285 1.351v.09c0 .075 0 .135.016.21.074 1.126.599 2.141 1.394 2.892.974.93 2.306 1.499 3.759 1.499.975 0 1.874-.255 2.668-.689.12-.061.239-.135.36-.21.194-.12.375-.255.554-.391.179-.134.344-.284.509-.435l.045-.044zm-5.741-5.041c.217 0 .389.18.389.397l.075 3.052-.075 2.243c0 .217-.172.397-.389.397-.217 0-.389-.18-.389-.397l-.075-2.243.075-3.052c0-.217.172-.397.389-.397z"/></svg>
                            SoundCloud
                        </a>` : ''}
                    </div>
                </div>
            `;
        }

        let galleriesHtml = '';
        if (galleries && galleries.length > 0) {
            galleriesHtml = `
                <div class="mt-6">
                    <h4 class="text-md font-medium text-gray-900 border-b border-gray-200 pb-2 mb-3">Gallery</h4>
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                        ${galleries.map(gallery => `
                            <div class="overflow-hidden rounded-lg">
                                <img src="${gallery.gambar}" alt="Gallery" class="w-full h-48 object-cover">
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        const mainImage = dj.gambar
            ? `<img src="${dj.gambar}" alt="${dj.nama}" class="w-full h-64 object-cover rounded-lg">`
            : '<div class="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center"><svg class="w-16 h-16 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path></svg></div>';

        const statusBadge = dj.flag_aktif == '1'
            ? '<span class="inline-flex px-2 py-1 text-xs font-semibold text-green-800 bg-green-100 rounded-full">Active</span>'
            : '<span class="inline-flex px-2 py-1 text-xs font-semibold text-red-800 bg-red-100 rounded-full">Inactive</span>';

        detailContent.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="md:col-span-1">
                    ${mainImage}
                </div>
                <div class="md:col-span-2">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-xl font-bold text-gray-900">${dj.nama}</h3>
                        ${statusBadge}
                    </div>

                    <p class="text-gray-600 mb-4">${dj.gender || ''} ${dj.origin ? `from ${dj.origin}` : ''}</p>

                    ${dj.deskripsi ? `
                        <div class="mb-4">
                            <h4 class="text-md font-medium text-gray-900 border-b border-gray-200 pb-2 mb-2">Description</h4>
                            <p class="text-gray-700">${dj.deskripsi}</p>
                        </div>
                    ` : ''}

                    ${dj.alamat ? `
                        <div class="mb-4">
                            <h4 class="text-md font-medium text-gray-900 border-b border-gray-200 pb-2 mb-2">Address</h4>
                            <p class="text-gray-700">${dj.alamat}</p>
                        </div>
                    ` : ''}
                </div>
            </div>

            ${socialLinks}
            ${galleriesHtml}
        `;

        showDetailModal();
    }

    // Fill edit form with DJ data
    function fillEditForm(dj) {
        console.log('Filling edit form with DJ data:', dj);

        document.getElementById('dj-id').value = dj.id;
        document.getElementById('nama').value = dj.nama || '';
        document.getElementById('origin').value = dj.origin || '';

        // Debug gender selection
        const genderSelect = document.getElementById('gender');
        console.log('DJ gender from data:', dj.gender);
        console.log('Available gender options:', Array.from(genderSelect.options).map(opt => opt.value));

        genderSelect.value = dj.gender || '';
        console.log('Gender select value after setting:', genderSelect.value);

        // If gender didn't set properly, try to match case-insensitive
        if (genderSelect.value === '' && dj.gender) {
            const options = Array.from(genderSelect.options);
            const matchingOption = options.find(opt =>
                opt.value.toLowerCase() === dj.gender.toLowerCase()
            );
            if (matchingOption) {
                genderSelect.value = matchingOption.value;
                console.log('Gender matched case-insensitive:', matchingOption.value);
            } else {
                console.warn('No matching gender option found for:', dj.gender);
            }
        }

        document.getElementById('alamat').value = dj.alamat || '';
        document.getElementById('deskripsi').value = dj.deskripsi || '';
        document.getElementById('flag_aktif').value = dj.flag_aktif;
        document.getElementById('spotify').value = dj.spotify || '';
        document.getElementById('fb').value = dj.fb || '';
        document.getElementById('ig').value = dj.ig || '';
        document.getElementById('yt').value = dj.yt || '';
        document.getElementById('yt_music').value = dj.yt_music || '';
        document.getElementById('soundcloud').value = dj.soundcloud || '';

        modalTitle.textContent = 'Edit DJ';
        saveDjBtn.textContent = 'Update DJ';

        showModal();
    }

    // Initial load
    loadDjs();
});
</script>
@endpush
