@extends('layouts.admin')

@section('title', 'Venues - Admin Panel')
@section('page-title', 'Venues')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Action Buttons -->
    <!-- <div class="flex flex-col sm:flex-row gap-3 sm:gap-4">
        <button id="add-venue-btn" class="flex items-center justify-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium text-sm">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Add New Venue</span>
        </button>
    </div> -->

    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Venues</h1>
            <p class="text-gray-600 mt-1">Manage your venues and activities</p>
        </div>
        <button id="add-venue-btn" class="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors font-medium text-sm flex items-center space-x-2">
            <span>Buat Venue</span>
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
            </svg>
        </button>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <!-- Status Filter -->
            <div class="flex items-center space-x-2">
                <select id="status-filter" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                    <option value="">All Status</option>
                    <option value="1" {{ $status == '1' ? 'selected' : '' }}>Active</option>
                    <option value="0" {{ $status == '0' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>

            <!-- Search -->
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <input type="text" id="search-filter" placeholder="Search venues..." value="{{ $search }}" class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                    <svg class="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Table -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 border-b border-gray-200">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Banner</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Venue Name</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Information</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody id="venue-table-body" class="bg-white divide-y divide-gray-200">
                    @forelse($venues as $venue)
                    <tr class="hover:bg-gray-50">
                        <td class="px-4 py-4 whitespace-nowrap">
                            @if($venue['banner'])
                                <img src="{{ asset($venue['banner']) }}" alt="Banner" class="w-12 h-12 rounded-lg object-cover">
                            @else
                                <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @endif
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ $venue['nama'] }}</div>
                            <!-- @if($venue['username_ig'])
                                <div class="text-sm text-gray-500">@{{ $venue['username_ig'] }}</div>
                            @endif -->
                        </td>
                        <td class="px-4 py-4">
                            <div class="text-sm text-gray-900 max-w-xs truncate">{{ $venue['alamat'] }}</div>
                            @if($venue['no_wa'])
                                <div class="text-sm text-gray-500">{{ $venue['no_wa'] }}</div>
                            @endif
                        </td>
                        <td class="px-4 py-4">
                            <div class="text-sm text-gray-900 max-w-xs">{{ $venue['informasi_display'] ?? $venue['informasi'] }}</div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            @if($venue['status_color'] == 'green')
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    {{ $venue['status'] }}
                                </span>
                            @else
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                    {{ $venue['status'] }}
                                </span>
                            @endif
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">{{ $venue['created_at'] }}</td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div class="flex space-x-2">
                                <button class="view-detail-btn text-blue-600 hover:text-blue-900"
                                        data-venue='@json($venue)'
                                        title="View Details">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                                <button class="edit-venue-btn text-green-600 hover:text-green-900"
                                        data-venue='@json($venue)'
                                        title="Edit Venue">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="px-4 py-12 text-center">
                            <div class="flex flex-col items-center justify-center space-y-3">
                                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <div class="text-gray-500">
                                    <p class="text-lg font-medium">No Venues data available</p>
                                    <p class="text-sm">Click "Add New Venue" to create your first venue.</p>
                                </div>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination Info and Load More -->
    <div class="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
        <div id="pagination-info" class="text-sm text-gray-500">
            Showing {{ count($venues) }} of {{ count($venues) }} results
        </div>
        <div id="load-more-container" class="hidden">
            <button id="load-more-btn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                <span id="load-more-text">Load More</span>
                <svg id="load-more-spinner" class="hidden animate-spin ml-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </button>
        </div>
    </div>
</div>

<!-- Add/Edit Venue Modal -->
<div id="venue-modal" class="fixed inset-0 bg-black/30 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center mb-4">
                <h3 id="modal-title" class="text-lg font-medium text-gray-900">Add New Venue</h3>
                <button id="close-venue-modal" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Content -->
            <form id="venue-form" enctype="multipart/form-data">
                @csrf
                <input type="hidden" id="venue-id" name="venue_id">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Left Column -->
                    <div class="space-y-4">
                        <!-- Venue Name -->
                        <div>
                            <label for="nama" class="block text-sm font-medium text-gray-700 mb-1">Venue Name *</label>
                            <input type="text" id="nama" name="nama" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                        </div>

                        <!-- Address -->
                        <div>
                            <label for="alamat" class="block text-sm font-medium text-gray-700 mb-1">Address *</label>
                            <textarea id="alamat" name="alamat" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"></textarea>
                        </div>

                        <!-- Information -->
                        <div>
                            <label for="informasi" class="block text-sm font-medium text-gray-700 mb-1">Information</label>
                            <textarea id="informasi" name="informasi" rows="6" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"></textarea>
                        </div>

                        <!-- Currency -->
                        <div>
                            <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">Currency *</label>
                            <select id="currency" name="currency" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                                <option value="IDR">IDR - Indonesian Rupiah</option>
                                <!-- <option value="USD">USD - US Dollar</option>
                                <option value="SGD">SGD - Singapore Dollar</option>
                                <option value="MYR">MYR - Malaysian Ringgit</option> -->
                            </select>
                        </div>

                        <!-- Location Dropdowns -->
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="id_propinsi" class="block text-sm font-medium text-gray-700 mb-1">Province</label>
                                <select id="id_propinsi" name="id_propinsi" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                                    <option value="">Select Province</option>
                                </select>
                            </div>
                            <div>
                                <label for="id_kota" class="block text-sm font-medium text-gray-700 mb-1">Regency/City</label>
                                <select id="id_kota" name="id_kota" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                                    <option value="">Select Regency/City</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label for="id_kelurahan" class="block text-sm font-medium text-gray-700 mb-1">District</label>
                            <select id="id_kelurahan" name="id_kelurahan" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                                <option value="">Select District</option>
                            </select>
                        </div>

                        <!-- Coordinates -->
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="lat" class="block text-sm font-medium text-gray-700 mb-1">Latitude</label>
                                <input type="number" step="any" id="lat" name="lat" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                            </div>
                            <div>
                                <label for="lng" class="block text-sm font-medium text-gray-700 mb-1">Longitude</label>
                                <input type="number" step="any" id="lng" name="lng" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="space-y-4">
                        <!-- Social Media -->
                        <div>
                            <label for="username_ig" class="block text-sm font-medium text-gray-700 mb-1">Instagram Username</label>
                            <input type="text" id="username_ig" name="username_ig" placeholder="without @" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                        </div>

                        <!-- WhatsApp -->
                        <div>
                            <label for="no_wa" class="block text-sm font-medium text-gray-700 mb-1">WhatsApp Number</label>
                            <input type="text" id="no_wa" name="no_wa" placeholder="+62..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="flag_aktif" class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
                            <select id="flag_aktif" name="flag_aktif" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                                <option value="1">Active</option>
                                <option value="0">Inactive</option>
                            </select>
                        </div>

                        <!-- Banner Upload -->
                        <div>
                            <label for="banner" class="block text-sm font-medium text-gray-700 mb-1">Banner Image</label>
                            <input type="file" id="banner" name="banner" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                            <p class="text-xs text-gray-500 mt-1">Max size: 2MB. Formats: JPG, PNG, GIF</p>
                        </div>

                        <!-- Gallery Upload -->
                        <div>
                            <label for="galleries" class="block text-sm font-medium text-gray-700 mb-1">Gallery Images</label>
                            <input type="file" id="galleries" name="galleries[]" accept="image/*" multiple class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                            <p class="text-xs text-gray-500 mt-1">Multiple images allowed. Max size: 2MB each.</p>
                        </div>
                    </div>
                </div>

                <!-- Open Hours Section -->
                <div class="mt-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Opening Hours</h4>
                    <div id="open-hours-container" class="space-y-3">
                        <!-- Open hours will be added dynamically -->
                    </div>
                    <button type="button" id="add-open-hour" class="mt-3 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                        Add Opening Hour
                    </button>
                </div>

                <!-- Ticket Types Section -->
                <div class="mt-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Ticket Types *</h4>
                    <div id="ticket-types-container" class="space-y-3">
                        <!-- Ticket types will be added dynamically -->
                    </div>
                    <button type="button" id="add-ticket-type" class="mt-3 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm">
                        Add Ticket Type
                    </button>
                </div>

                <!-- Modal Actions -->
                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t">
                    <button type="button" id="cancel-venue-btn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">Cancel</button>
                    <button type="submit" id="save-venue-btn" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">Save Venue</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Detail Venue Modal -->
<div id="detail-venue-modal" class="fixed inset-0 bg-black/30 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Venue Details</h3>
                <button id="close-detail-modal" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Content -->
            <div id="detail-content" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Custom Alert Modal -->
<div id="custom-alert" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div id="alert-icon" class="mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4">
                <!-- Icon will be set by JavaScript -->
            </div>
            <h3 id="alert-title" class="text-lg font-medium text-gray-900 mb-2"></h3>
            <div id="alert-message" class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500"></p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="alert-ok-btn" class="px-4 py-2 text-white text-base font-medium rounded-md w-full shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2">
                    OK
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Custom Alert Class
class CustomAlert {
    constructor() {
        this.modal = document.getElementById('custom-alert');
        this.icon = document.getElementById('alert-icon');
        this.title = document.getElementById('alert-title');
        this.message = document.getElementById('alert-message').querySelector('p');
        this.okBtn = document.getElementById('alert-ok-btn');

        this.okBtn.addEventListener('click', () => this.hide());
    }

    show(type, title, message) {
        this.title.textContent = title;
        this.message.textContent = message;

        // Reset classes
        this.icon.className = 'mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4';
        this.okBtn.className = 'px-4 py-2 text-white text-base font-medium rounded-md w-full shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2';

        // Set icon and colors based on type
        switch(type) {
            case 'success':
                this.icon.classList.add('bg-green-100');
                this.icon.innerHTML = '<svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
                this.okBtn.classList.add('bg-green-500', 'hover:bg-green-700', 'focus:ring-green-300');
                break;
            case 'error':
                this.icon.classList.add('bg-red-100');
                this.icon.innerHTML = '<svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
                this.okBtn.classList.add('bg-red-500', 'hover:bg-red-700', 'focus:ring-red-300');
                break;
            case 'warning':
                this.icon.classList.add('bg-yellow-100');
                this.icon.innerHTML = '<svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg>';
                this.okBtn.classList.add('bg-yellow-500', 'hover:bg-yellow-700', 'focus:ring-yellow-300');
                break;
            case 'info':
            default:
                this.icon.classList.add('bg-blue-100');
                this.icon.innerHTML = '<svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>';
                this.okBtn.classList.add('bg-blue-500', 'hover:bg-blue-700', 'focus:ring-blue-300');
                break;
        }

        this.modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    hide() {
        this.modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    // Convenience methods
    success(title, message) {
        return this.show('success', title, message);
    }

    error(title, message) {
        return this.show('error', title, message);
    }

    warning(title, message) {
        return this.show('warning', title, message);
    }

    info(title, message) {
        return this.show('info', title, message);
    }
}

// Initialize custom alert
const customAlert = new CustomAlert();
document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const addVenueBtn = document.getElementById('add-venue-btn');
    const venueModal = document.getElementById('venue-modal');
    const closeVenueModal = document.getElementById('close-venue-modal');
    const cancelVenueBtn = document.getElementById('cancel-venue-btn');
    const venueForm = document.getElementById('venue-form');
    const modalTitle = document.getElementById('modal-title');
    const saveVenueBtn = document.getElementById('save-venue-btn');
    const addOpenHourBtn = document.getElementById('add-open-hour');
    const openHoursContainer = document.getElementById('open-hours-container');

    // Ticket types elements
    const ticketTypesContainer = document.getElementById('ticket-types-container');
    const addTicketTypeBtn = document.getElementById('add-ticket-type');



    // Detail modal elements
    const detailModal = document.getElementById('detail-venue-modal');
    const closeDetailModal = document.getElementById('close-detail-modal');
    const detailContent = document.getElementById('detail-content');

    // Location dropdowns
    const provinceSelect = document.getElementById('id_propinsi');
    const regencySelect = document.getElementById('id_kota');
    const districtSelect = document.getElementById('id_kelurahan');

    // Search and filter elements
    const searchFilter = document.getElementById('search-filter');
    const statusFilter = document.getElementById('status-filter');
    const tableBody = document.getElementById('venue-table-body');
    const paginationInfo = document.getElementById('pagination-info');

    // Load more elements
    const loadMoreContainer = document.getElementById('load-more-container');
    const loadMoreBtn = document.getElementById('load-more-btn');
    const loadMoreText = document.getElementById('load-more-text');
    const loadMoreSpinner = document.getElementById('load-more-spinner');

    // Days of week for open hours
    const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

    // Pagination state
    let currentPage = 1;
    let isLoading = false;
    let hasMore = true;

    // Load provinces on page load
    loadProvinces();

    // Location dropdown functions
    function loadProvinces() {
        fetch('{{ route("admin.venues.provinces") }}', {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                provinceSelect.innerHTML = '<option value="">Select Province</option>';
                data.provinces.forEach(province => {
                    provinceSelect.innerHTML += `<option value="${province.id}">${province.name}</option>`;
                });
            }
        })
        .catch(error => {
            console.error('Error loading provinces:', error);
        });
    }

    function loadRegencies(provinceId) {
        regencySelect.innerHTML = '<option value="">Loading...</option>';
        districtSelect.innerHTML = '<option value="">Select District</option>';

        return fetch(`{{ route("admin.venues.regencies") }}?province_id=${provinceId}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                regencySelect.innerHTML = '<option value="">Select Regency/City</option>';
                data.regencies.forEach(regency => {
                    regencySelect.innerHTML += `<option value="${regency.id}">${regency.name}</option>`;
                });
            }
        })
        .catch(error => {
            console.error('Error loading regencies:', error);
            regencySelect.innerHTML = '<option value="">Error loading data</option>';
        });
    }

    function loadDistricts(regencyId) {
        districtSelect.innerHTML = '<option value="">Loading...</option>';

        return fetch(`{{ route("admin.venues.districts") }}?regency_id=${regencyId}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                districtSelect.innerHTML = '<option value="">Select District</option>';
                data.districts.forEach(district => {
                    districtSelect.innerHTML += `<option value="${district.id}">${district.name}</option>`;
                });
            }
        })
        .catch(error => {
            console.error('Error loading districts:', error);
            districtSelect.innerHTML = '<option value="">Error loading data</option>';
        });
    }

    // Modal functions
    function showModal() {
        venueModal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    function hideModal() {
        venueModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
        resetForm();
    }

    function showDetailModal() {
        detailModal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    function hideDetailModal() {
        detailModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    function showVenueDetail(venue) {
        // Load open hours for this venue
        fetch(`/admin/venues/${venue.id}/open-hours`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            let openHoursHtml = '';
            if (data.success && data.open_hours.length > 0) {
                openHoursHtml = data.open_hours.map(hour =>
                    `<div class="flex justify-between py-1">
                        <span class="font-medium">${hour.hari}:</span>
                        <span>${hour.jam_buka} - ${hour.jam_tutup}</span>
                    </div>`
                ).join('');
            } else {
                openHoursHtml = '<p class="text-gray-500 text-sm">No opening hours set</p>';
            }

            detailContent.innerHTML = `
                <div class="space-y-4">
                    <div class="text-center">
                        ${venue.banner ?
                            `<img src="${venue.banner}" alt="Banner" class="w-full h-48 object-cover rounded-lg mb-4">` :
                            `<div class="w-full h-48 bg-gray-200 rounded-lg flex items-center justify-center mb-4">
                                <svg class="w-16 h-16 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                </svg>
                            </div>`
                        }
                        <h2 class="text-xl font-bold text-gray-900">${venue.nama}</h2>
                        ${venue.username_ig ? `<p class="text-gray-600">@${venue.username_ig}</p>` : ''}
                    </div>

                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">Basic Information</h3>
                            <div class="bg-gray-50 p-3 rounded-lg space-y-2">
                                <div class="flex justify-between">
                                    <span class="font-medium">Status:</span>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${venue.status_color === 'green' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">${venue.status}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium">Currency:</span>
                                    <span>${venue.currency}</span>
                                </div>
                                ${venue.no_wa ? `<div class="flex justify-between"><span class="font-medium">WhatsApp:</span><span>${venue.no_wa}</span></div>` : ''}
                                ${venue.lat && venue.lng ? `<div class="flex justify-between"><span class="font-medium">Coordinates:</span><span>${venue.lat}, ${venue.lng}</span></div>` : ''}
                            </div>
                        </div>

                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">Address</h3>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-gray-700">${venue.alamat}</p>
                            </div>
                        </div>

                        ${venue.informasi ? `
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">Information</h3>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-gray-700">${venue.informasi}</p>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>

                <div class="space-y-4">
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-2">Opening Hours</h3>
                        <div class="bg-gray-50 p-3 rounded-lg">
                            ${openHoursHtml}
                        </div>
                    </div>

                    <div>
                        <h3 class="font-semibold text-gray-900 mb-2">Created</h3>
                        <div class="bg-gray-50 p-3 rounded-lg">
                            <p class="text-gray-700">${venue.created_at}</p>
                        </div>
                    </div>
                </div>
            `;

            showDetailModal();
        })
        .catch(error => {
            console.error('Error loading venue details:', error);
            customAlert.error('Error', 'Failed to load venue details');
        });
    }

    function resetForm() {
        venueForm.reset();
        document.getElementById('venue-id').value = '';
        modalTitle.textContent = 'Add New Venue';
        saveVenueBtn.textContent = 'Save Venue';
        openHoursContainer.innerHTML = '';
        ticketTypesContainer.innerHTML = '';
        addDefaultOpenHour();
        addDefaultTicketType();
    }

    // Open hours functions
    function addOpenHour(hari = '', jamBuka = '', jamTutup = '', id = '') {
        const index = openHoursContainer.children.length;
        const openHourHtml = `
            <div class="open-hour-item flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                ${id ? `<input type="hidden" name="open_hours[${index}][id]" value="${id}">` : ''}
                <select name="open_hours[${index}][hari]" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                    <option value="">Select Day</option>
                    ${daysOfWeek.map(day => `<option value="${day}" ${day === hari ? 'selected' : ''}>${day}</option>`).join('')}
                </select>
                <input type="time" name="open_hours[${index}][jam_buka]" value="${jamBuka}" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                <span class="text-gray-500">to</span>
                <input type="time" name="open_hours[${index}][jam_tutup]" value="${jamTutup}" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                <button type="button" class="remove-open-hour text-red-600 hover:text-red-800">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `;
        openHoursContainer.insertAdjacentHTML('beforeend', openHourHtml);
    }

    function addDefaultOpenHour() {
        addOpenHour();
    }

    function cleanupEmptyOpenHours() {
        const openHourItems = openHoursContainer.querySelectorAll('.open-hour-item');
        openHourItems.forEach(item => {
            const daySelect = item.querySelector('select[name*="[hari]"]');
            if (!daySelect.value) {
                item.remove();
            }
        });
    }

    // Ticket types functions
    function addTicketType(namaTicket = '', noteTicket = '', harga = '', stok = '', id = '', flagAktif = '1') {
        const index = ticketTypesContainer.children.length;
        const isActive = flagAktif === '1';
        const ticketTypeHtml = `
            <div class="ticket-type-item flex items-center space-x-3 p-3 bg-gray-50 rounded-lg ${!isActive ? 'opacity-60' : ''}">
                ${id ? `<input type="hidden" name="ticket_types[${index}][id]" value="${id}">` : ''}
                <div class="flex-1">
                    <input type="text" name="ticket_types[${index}][nama_tiket]" value="${namaTicket}" placeholder="Ticket Name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                </div>
                <div class="flex-1">
                    <input type="text" name="ticket_types[${index}][note_tiket]" value="${noteTicket}" placeholder="Description" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                </div>
                <div class="w-32">
                    <input type="number" name="ticket_types[${index}][default_harga]" value="${harga}" placeholder="Price" required min="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                </div>
                <div class="w-24">
                    <input type="number" name="ticket_types[${index}][default_stok]" value="${stok}" placeholder="Stock" required min="1" class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                </div>
                <div class="flex items-center space-x-2">
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="hidden" name="ticket_types[${index}][flag_aktif]" value="0">
                        <input type="checkbox" name="ticket_types[${index}][flag_aktif]" value="1" ${isActive ? 'checked' : ''} class="sr-only peer" onchange="toggleTicketStatus(this)">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                    </label>
                    <span class="text-xs text-gray-600">${isActive ? 'Active' : 'Inactive'}</span>
                </div>
                <button type="button" class="remove-ticket-type text-red-600 hover:text-red-800" title="Remove Ticket">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `;
        ticketTypesContainer.insertAdjacentHTML('beforeend', ticketTypeHtml);
    }

    function addDefaultTicketType() {
        addTicketType();
    }

    function toggleTicketStatus(checkbox) {
        const ticketItem = checkbox.closest('.ticket-type-item');
        const statusText = ticketItem.querySelector('span');

        if (checkbox.checked) {
            ticketItem.classList.remove('opacity-60');
            statusText.textContent = 'Active';
        } else {
            ticketItem.classList.add('opacity-60');
            statusText.textContent = 'Inactive';
        }
    }

    function validateOpenHours() {
        const openHourItems = openHoursContainer.querySelectorAll('.open-hour-item');
        const selectedDays = [];
        let isValid = true;

        console.log('Validating open hours, found items:', openHourItems.length);

        openHourItems.forEach((item, index) => {
            const daySelect = item.querySelector('select[name*="[hari]"]');
            const day = daySelect.value;

            console.log(`Item ${index}: day = "${day}"`);

            // Clear previous error state
            daySelect.classList.remove('border-red-500');
            const existingErrorMsg = item.querySelector('.day-error');
            if (existingErrorMsg) {
                existingErrorMsg.remove();
            }

            if (day) {
                if (selectedDays.includes(day)) {
                    console.log(`Duplicate day detected: "${day}"`);
                    daySelect.classList.add('border-red-500');
                    isValid = false;

                    // Show error message
                    const errorMsg = document.createElement('div');
                    errorMsg.className = 'day-error text-red-500 text-xs mt-1';
                    errorMsg.textContent = 'This day is already selected';
                    daySelect.parentNode.appendChild(errorMsg);
                } else {
                    selectedDays.push(day);
                    console.log(`Added day "${day}" to selected days. Total: ${selectedDays.length}`);
                }
            }
        });

        console.log('Validation result:', isValid ? 'VALID' : 'INVALID');
        console.log('Selected days:', selectedDays);

        return isValid;
    }

    function cleanupEmptyTicketTypes() {
        const ticketTypeItems = ticketTypesContainer.querySelectorAll('.ticket-type-item');
        ticketTypeItems.forEach(item => {
            const nameInput = item.querySelector('input[name*="[nama_tiket]"]');
            const priceInput = item.querySelector('input[name*="[default_harga]"]');
            const stockInput = item.querySelector('input[name*="[default_stok]"]');
            if (!nameInput.value || !priceInput.value || !stockInput.value) {
                item.remove();
            }
        });
    }



    // Event listeners
    addVenueBtn.addEventListener('click', function() {
        resetForm();
        showModal();
    });

    closeVenueModal.addEventListener('click', hideModal);
    cancelVenueBtn.addEventListener('click', hideModal);
    closeDetailModal.addEventListener('click', hideDetailModal);

    venueModal.addEventListener('click', function(e) {
        if (e.target === venueModal) hideModal();
    });

    detailModal.addEventListener('click', function(e) {
        if (e.target === detailModal) hideDetailModal();
    });

    // Location dropdown event listeners
    provinceSelect.addEventListener('change', function() {
        if (this.value) {
            loadRegencies(this.value);
        } else {
            regencySelect.innerHTML = '<option value="">Select Regency/City</option>';
            districtSelect.innerHTML = '<option value="">Select District</option>';
        }
    });

    regencySelect.addEventListener('change', function() {
        if (this.value) {
            loadDistricts(this.value);
        } else {
            districtSelect.innerHTML = '<option value="">Select District</option>';
        }
    });

    addOpenHourBtn.addEventListener('click', function() {
        // Check if we already have 7 open hours (max days in a week)
        const currentItems = openHoursContainer.querySelectorAll('.open-hour-item');
        if (currentItems.length >= 7) {
            customAlert.warning('Maximum Reached', 'You can only add up to 7 opening hours (one for each day of the week).');
            return;
        }

        addOpenHour();
        console.log('Added new open hour. Total items:', currentItems.length + 1);
    });

    addTicketTypeBtn.addEventListener('click', function() {
        addTicketType();
    });

    // Remove open hour event delegation
    openHoursContainer.addEventListener('click', function(e) {
        if (e.target.closest('.remove-open-hour')) {
            e.target.closest('.open-hour-item').remove();
            validateOpenHours(); // Revalidate after removal
        }
    });

    // Validate opening hours on day selection change
    openHoursContainer.addEventListener('change', function(e) {
        if (e.target.matches('select[name*="[hari]"]')) {
            console.log('Day selection changed, validating...');
            const isValid = validateOpenHours();

            if (!isValid) {
                // Show immediate feedback
                customAlert.warning('Duplicate Day', 'You have selected the same day multiple times. Please choose different days for each opening hour.');
            }
        }
    });

    // Remove ticket type event delegation
    ticketTypesContainer.addEventListener('click', function(e) {
        if (e.target.closest('.remove-ticket-type')) {
            const ticketItem = e.target.closest('.ticket-type-item');
            const idInput = ticketItem.querySelector('input[name*="[id]"]');

            if (idInput && idInput.value) {
                // For existing tickets, mark as inactive instead of removing
                const checkbox = ticketItem.querySelector('input[name*="[flag_aktif]"]');
                if (checkbox) {
                    checkbox.checked = false;
                    toggleTicketStatus(checkbox);

                    // Show confirmation
                    customAlert.warning('Ticket Deactivated', 'Existing ticket has been deactivated instead of deleted to preserve data integrity.');
                }
            } else {
                // For new tickets, remove completely
                ticketItem.remove();
            }
        }
    });

    // View detail button event delegation
    document.addEventListener('click', function(e) {
        if (e.target.closest('.view-detail-btn')) {
            const btn = e.target.closest('.view-detail-btn');
            const venue = JSON.parse(btn.dataset.venue);
            showVenueDetail(venue);
        }
    });

    // Edit venue button event delegation
    document.addEventListener('click', function(e) {
        if (e.target.closest('.edit-venue-btn')) {
            const btn = e.target.closest('.edit-venue-btn');
            const venue = JSON.parse(btn.dataset.venue);
            editVenue(venue);
        }
    });



    function editVenue(venue) {
        console.log('Editing venue with data:', venue);
        console.log('Venue informasi field:', venue.informasi);

        modalTitle.textContent = 'Edit Venue';
        saveVenueBtn.textContent = 'Update Venue';

        // Fill form with venue data
        document.getElementById('venue-id').value = venue.id;
        document.getElementById('nama').value = venue.nama;
        document.getElementById('alamat').value = venue.alamat;

        // Debug informasi field
        const informasiField = document.getElementById('informasi');
        const informasiValue = venue.informasi || '';
        console.log('Setting informasi field to:', informasiValue);
        informasiField.value = informasiValue;
        console.log('Informasi field value after setting:', informasiField.value);

        document.getElementById('currency').value = venue.currency;
        document.getElementById('lat').value = venue.lat || '';
        document.getElementById('lng').value = venue.lng || '';
        document.getElementById('username_ig').value = venue.username_ig || '';
        document.getElementById('no_wa').value = venue.no_wa || '';
        document.getElementById('flag_aktif').value = venue.flag_aktif;

        // Set location dropdowns
        if (venue.id_propinsi) {
            provinceSelect.value = venue.id_propinsi;
            loadRegencies(venue.id_propinsi).then(() => {
                if (venue.id_kota) {
                    regencySelect.value = venue.id_kota;
                    loadDistricts(venue.id_kota).then(() => {
                        if (venue.id_kelurahan) {
                            districtSelect.value = venue.id_kelurahan;
                        }
                    });
                }
            });
        }

        // Load open hours and ticket types via AJAX
        openHoursContainer.innerHTML = '';
        ticketTypesContainer.innerHTML = '';
        loadOpenHours(venue.id);
        loadTicketTypes(venue.id);

        showModal();
    }

    function loadOpenHours(venueId) {
        fetch(`/admin/venues/${venueId}/open-hours`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.open_hours.length > 0) {
                data.open_hours.forEach(hour => {
                    addOpenHour(hour.hari, hour.jam_buka, hour.jam_tutup, hour.id);
                });
            } else {
                addDefaultOpenHour();
            }
        })
        .catch(error => {
            console.error('Error loading open hours:', error);
            addDefaultOpenHour();
        });
    }

    function loadTicketTypes(venueId) {
        fetch(`/admin/venues/${venueId}/ticket-types`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.ticket_types.length > 0) {
                data.ticket_types.forEach(ticket => {
                    addTicketType(ticket.nama_tiket, ticket.note_tiket, ticket.default_harga, ticket.default_stok, ticket.id, ticket.flag_aktif);
                });
            } else {
                addDefaultTicketType();
            }
        })
        .catch(error => {
            console.error('Error loading ticket types:', error);
            addDefaultTicketType();
        });
    }



    // Form submission
    venueForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate opening hours first
        if (!validateOpenHours()) {
            customAlert.error('Validation Error', 'Please fix the duplicate opening hours before submitting.');
            return;
        }

        // Clean up empty open hours and ticket types before submitting
        cleanupEmptyOpenHours();
        cleanupEmptyTicketTypes();

        const formData = new FormData(venueForm);
        const isEdit = document.getElementById('venue-id').value !== '';
        const venueId = document.getElementById('venue-id').value;

        saveVenueBtn.disabled = true;
        saveVenueBtn.textContent = isEdit ? 'Updating...' : 'Saving...';

        const url = isEdit ? `/admin/venues/${venueId}` : '{{ route("admin.venues.store") }}';
        const method = isEdit ? 'PUT' : 'POST';

        // Add method override for PUT requests
        if (isEdit) {
            formData.append('_method', 'PUT');
        }

        fetch(url, {
            method: 'POST', // Always POST, but with _method override for PUT
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                customAlert.success('Success', data.message);
                hideModal();
                location.reload(); // Reload page to show updated data
            } else {
                customAlert.error('Error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            customAlert.error('Error', 'An error occurred while saving the venue.');
        })
        .finally(() => {
            saveVenueBtn.disabled = false;
            saveVenueBtn.textContent = isEdit ? 'Update Venue' : 'Save Venue';
        });
    });

    // Search and filter functionality
    let searchTimeout;
    searchFilter.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            currentPage = 1;
            loadVenues(false);
        }, 500);
    });

    statusFilter.addEventListener('change', function() {
        currentPage = 1;
        loadVenues(false);
    });

    loadMoreBtn.addEventListener('click', function() {
        if (!isLoading && hasMore) {
            currentPage++;
            loadVenues(true); // true = append mode
        }
    });

    function loadVenues(append = false) {
        if (isLoading) return;

        isLoading = true;

        // Reset pagination if not appending
        if (!append) {
            currentPage = 1;
            hasMore = true;
        }

        // Show loading state
        if (append) {
            // Show loading in load more button
            loadMoreText.textContent = 'Loading...';
            loadMoreSpinner.classList.remove('hidden');
            loadMoreBtn.disabled = true;
        } else {
            // Show loading in table
            tableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="px-4 py-8 text-center text-gray-500">
                        <div class="flex items-center justify-center space-x-2">
                            <svg class="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span>Loading venues...</span>
                        </div>
                    </td>
                </tr>
            `;
        }

        // Prepare data
        const params = new URLSearchParams({
            search: searchFilter.value,
            status: statusFilter.value,
            page: currentPage
        });

        // Make AJAX request
        fetch(`{{ route("admin.venues") }}?${params}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.venues && data.venues.length > 0) {
                    let html = '';
                    data.venues.forEach(venue => {
                        html += createVenueRow(venue);
                    });

                    if (append) {
                        // Append new rows to existing table
                        tableBody.insertAdjacentHTML('beforeend', html);
                    } else {
                        // Replace table content
                        tableBody.innerHTML = html;
                    }

                    // Update pagination info
                    const pagination = data.pagination;
                    hasMore = pagination.has_more;

                    paginationInfo.innerHTML = `Showing ${pagination.showing_from}-${pagination.showing_to} of ${pagination.total_count} results`;

                    // Show/hide load more button
                    if (hasMore) {
                        loadMoreContainer.classList.remove('hidden');
                    } else {
                        loadMoreContainer.classList.add('hidden');
                    }

                } else if (!append) {
                    // Only show empty state if not appending
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="7" class="px-4 py-12 text-center">
                                <div class="flex flex-col items-center justify-center space-y-3">
                                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    <div class="text-gray-500">
                                        <p class="text-lg font-medium">No Venues data available</p>
                                        <p class="text-sm">Click "Add New Venue" to create your first venue.</p>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    `;
                    paginationInfo.innerHTML = 'No results found';
                    loadMoreContainer.classList.add('hidden');
                }
            } else {
                throw new Error(data.error || 'Unknown error occurred');
            }
        })
        .catch(error => {
            console.error('Error loading venues:', error);

            if (!append) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="px-4 py-8 text-center text-red-500">
                            <div class="flex flex-col items-center space-y-2">
                                <svg class="w-12 h-12 text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>Error: ${error.message}</span>
                            </div>
                        </td>
                    </tr>
                `;
                paginationInfo.innerHTML = 'Error loading data';
            }

            // Reset current page if append failed
            if (append) {
                currentPage--;
            }
        })
        .finally(() => {
            // Reset loading state
            isLoading = false;

            if (append) {
                loadMoreText.textContent = 'Load More';
                loadMoreSpinner.classList.add('hidden');
                loadMoreBtn.disabled = false;
            }
        });
    }

    function createVenueRow(venue) {
        const bannerImg = venue.banner
            ? `<img src="${venue.banner}" alt="Banner" class="w-12 h-12 rounded-lg object-cover">`
            : `<div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                </svg>
               </div>`;

        const statusBadge = venue.status_color === 'green'
            ? `<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">${venue.status}</span>`
            : `<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">${venue.status}</span>`;

        return `
            <tr class="hover:bg-gray-50">
                <td class="px-4 py-4 whitespace-nowrap">${bannerImg}</td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">${venue.nama}</div>
                    ${venue.username_ig ? `<div class="text-sm text-gray-500">@${venue.username_ig}</div>` : ''}
                </td>
                <td class="px-4 py-4">
                    <div class="text-sm text-gray-900 max-w-xs truncate">${venue.alamat}</div>
                    ${venue.no_wa ? `<div class="text-sm text-gray-500">${venue.no_wa}</div>` : ''}
                </td>
                <td class="px-4 py-4">
                    <div class="text-sm text-gray-900 max-w-xs">${venue.informasi_display || venue.informasi}</div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">${statusBadge}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">${venue.created_at}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div class="flex space-x-2">
                        <button class="view-detail-btn text-blue-600 hover:text-blue-900" data-venue='${JSON.stringify(venue)}' title="View Details">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                        <button class="edit-venue-btn text-green-600 hover:text-green-900" data-venue='${JSON.stringify(venue)}' title="Edit Venue">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                            </svg>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    // Initialize with default open hour and ticket type
    addDefaultOpenHour();
    addDefaultTicketType();
});
</script>

@endsection
