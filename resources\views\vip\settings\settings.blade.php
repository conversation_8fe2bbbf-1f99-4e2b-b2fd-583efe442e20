@extends('vip.app')
@section('title', 'Settings - KREEN VIP')

@push('styles')
<style>
/* Full width section */
.full-width-section {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    padding-left: calc(50vw - 50%);
    padding-right: calc(50vw - 50%);
}

/* Settings container */
.settings-container {
    max-width: 600px;
    margin: 0 auto;
}

/* Accordion section */
.accordion-section {
    background: linear-gradient(135deg, rgba(60, 60, 60, 0.9), rgba(80, 80, 80, 0.9));
    border-radius: 0.75rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.accordion-header {
    padding: 1.25rem 1.5rem;
    background: rgba(40, 40, 40, 0.8);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.accordion-header:hover {
    background: rgba(50, 50, 50, 0.8);
}

.accordion-header h3 {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    flex: 1;
}

.accordion-icon {
    width: 20px;
    height: 20px;
    color: #D4AF37;
    transition: transform 0.3s ease;
}

.accordion-icon.rotated {
    transform: rotate(180deg);
}

.accordion-content {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.accordion-content.active {
    padding: 1.5rem;
    max-height: 1000px;
}

/* Form styling */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    color: white;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #D4AF37;
    background: rgba(255, 255, 255, 0.15);
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-input:read-only {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.7);
}

/* Gender selection */
.gender-selection {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.gender-option {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.75rem;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gender-option:hover {
    border-color: #D4AF37;
}

.gender-option.selected {
    border-color: #D4AF37;
    background: rgba(212, 175, 55, 0.1);
}

.gender-avatar {
    width: 60px;
    height: 60px;
    margin: 0 auto 0.5rem;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
}

.gender-label {
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Profile picture upload */
.profile-upload {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.profile-preview {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;
}

.profile-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-preview svg {
    width: 40px;
    height: 40px;
    color: rgba(255, 255, 255, 0.5);
}

.upload-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    color: white;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-button:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: #D4AF37;
}

/* Save button */
.save-button {
    background: #D4AF37;
    color: black;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-button:hover {
    background: #B8941F;
    transform: scale(1.05);
}

.save-button:disabled {
    background: #666;
    color: #999;
    cursor: not-allowed;
    transform: none;
}

/* Error and success messages */
.alert {
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.alert-success {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}
</style>
@endpush

@section('content')
<!-- Container dengan Background Image -->
<div class="explore-nights-bg-container">
    <!-- Settings Section -->
    <section class="explore-nights-content py-12">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            
            <!-- Page Title -->
            <div class="settings-container">
            <div class="text-first mb-12">
                <h1 class="text-white text-4xl font-bold mb-4">Settings</h1>
            </div>
                
                <!-- Change Personal Information -->
                <div class="accordion-section">
                    <div class="accordion-header" onclick="toggleAccordion('personal-info')">
                        <h3>Change Personal Information</h3>
                        <svg class="accordion-icon" id="personal-info-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="accordion-content" id="personal-info-content">
                        <div id="personal-info-alert"></div>
                        <form id="personal-info-form" enctype="multipart/form-data">
                            @csrf
                            <!-- Full Name -->
                            <div class="form-group">
                                <label class="form-label">Full Name*</label>
                                <input type="text" name="name" class="form-input" value="{{ $user->name ?? '' }}" placeholder="Enter your full name" required>
                            </div>
                            
                            <!-- Phone -->
                            <div class="form-group">
                                <label class="form-label">Phone Number</label>
                                <input type="tel" name="phone" class="form-input" value="{{ $user->phone ?? '' }}" placeholder="Enter your phone number">
                            </div>
                            
                            <!-- Gender Selection -->
                            <div class="form-group">
                                <label class="form-label">Gender*</label>
                                <div class="gender-selection">
                                    <div class="gender-option {{ ($user->gender ?? '') === 'male' ? 'selected' : '' }}" onclick="selectGender('male')">
                                        <div class="gender-avatar">👨</div>
                                        <div class="gender-label">Male</div>
                                    </div>
                                    <div class="gender-option {{ ($user->gender ?? '') === 'female' ? 'selected' : '' }}" onclick="selectGender('female')">
                                        <div class="gender-avatar">👩</div>
                                        <div class="gender-label">Female</div>
                                    </div>
                                </div>
                                <input type="hidden" name="gender" value="{{ $user->gender ?? '' }}" required>
                            </div>
                            
                            <!-- Profile Picture -->
                            <div class="form-group">
                                <label class="form-label">Profile Picture</label>
                                <div class="profile-upload">
                                    <div class="profile-preview" id="profile-preview">
                                        @if(isset($user->profile_picture) && $user->profile_picture)
                                            <img src="{{ Storage::url($user->profile_picture) }}" alt="Profile Picture">
                                        @else
                                            <svg fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"/>
                                            </svg>
                                        @endif
                                    </div>
                                    <button type="button" class="upload-button" onclick="uploadProfilePicture()">
                                        Choose file to upload
                                    </button>
                                    <input type="file" name="profile_picture" id="profile-picture-input" accept="image/*" style="display: none;">
                                </div>
                            </div>
                            <button type="submit" class="save-button">Save</button>
                        </form>
                    </div>
                </div>

                <!-- Change Password -->
                <div class="accordion-section">
                    <div class="accordion-header" onclick="toggleAccordion('change-password')">
                        <h3>Change Password</h3>
                        <svg class="accordion-icon" id="change-password-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="accordion-content" id="change-password-content">
                        <div id="password-alert"></div>
                        <form id="password-form">
                            @csrf
                            <!-- Current Password -->
                            <div class="form-group">
                                <label class="form-label">Current Password*</label>
                                <input type="password" name="current_password" class="form-input" placeholder="Enter your current password" required>
                            </div>
                            <!-- New Password -->
                            <div class="form-group">
                                <label class="form-label">New Password*</label>
                                <input type="password" name="new_password" class="form-input" placeholder="Enter your new password (min 8 characters)" required>
                                <small style="color: rgba(255,255,255,0.7); font-size: 0.75rem;">
                                    Password must contain at least 8 characters with uppercase, lowercase, numbers, and symbols.
                                </small>
                            </div>
                            <!-- Confirm New Password -->
                            <div class="form-group">
                                <label class="form-label">Confirm New Password*</label>
                                <input type="password" name="confirm_password" class="form-input" placeholder="Confirm your new password" required>
                            </div>
                            <button type="submit" class="save-button">Save</button>
                        </form>
                    </div>
                </div>

                <!-- Change Email -->
                <div class="accordion-section">
                    <div class="accordion-header" onclick="toggleAccordion('change-email')">
                        <h3>Change Email</h3>
                        <svg class="accordion-icon" id="change-email-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="accordion-content" id="change-email-content">
                        <!-- Current Email -->
                        <div class="form-group">
                            <label class="form-label">Current Email</label>
                            <input type="email" class="form-input" value="{{ $user->email ?? '' }}" readonly>
                        </div>

                        <!-- New Email -->
                        <div class="form-group">
                            <label class="form-label">New Email*</label>
                            <input type="email" class="form-input" placeholder="Enter your new email">
                        </div>

                        <!-- Confirm Password -->
                        <div class="form-group">
                            <label class="form-label">Confirm Password*</label>
                            <input type="password" class="form-input" placeholder="Enter your password to confirm">
                        </div>

                        <button class="save-button" onclick="saveEmail()">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
let selectedGender = '{{ $user->gender ?? "" }}';

function toggleAccordion(sectionId) {
    const content = document.getElementById(sectionId + '-content');
    const icon = document.getElementById(sectionId + '-icon');
    
    // Close all other accordions
    const allContents = document.querySelectorAll('.accordion-content');
    const allIcons = document.querySelectorAll('.accordion-icon');
    
    allContents.forEach(item => {
        if (item.id !== sectionId + '-content') {
            item.classList.remove('active');
        }
    });
    
    allIcons.forEach(item => {
        if (item.id !== sectionId + '-icon') {
            item.classList.remove('rotated');
        }
    });
    
    // Toggle current accordion
    content.classList.toggle('active');
    icon.classList.toggle('rotated');
}

function selectGender(gender) {
    const options = document.querySelectorAll('.gender-option');
    options.forEach(option => option.classList.remove('selected'));
    
    selectedGender = gender;
    document.querySelector('input[name="gender"]').value = gender;
    
    if (gender === 'male') {
        options[0].classList.add('selected');
    } else {
        options[1].classList.add('selected');
    }
}

function uploadProfilePicture() {
    document.getElementById('profile-picture-input').click();
}

// Handle profile picture preview
document.getElementById('profile-picture-input').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('profile-preview');
            preview.innerHTML = `<img src="${e.target.result}" alt="Profile Picture">`;
        };
        reader.readAsDataURL(file);
    }
});

// Handle Personal Info Form
document.getElementById('personal-info-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const button = this.querySelector('.save-button');
    const alertDiv = document.getElementById('personal-info-alert');
    
    button.disabled = true;
    button.textContent = 'Saving...';
    this.classList.add('loading');
    
    fetch('{{ route("settings.update.personal") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alertDiv.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
        } else {
            let errorMessage = data.message;
            if (data.errors) {
                errorMessage += '<ul>';
                Object.values(data.errors).forEach(errors => {
                    errors.forEach(error => {
                        errorMessage += `<li>${error}</li>`;
                    });
                });
                errorMessage += '</ul>';
            }
            alertDiv.innerHTML = `<div class="alert alert-error">${errorMessage}</div>`;
        }
    })
    .catch(error => {
        alertDiv.innerHTML = `<div class="alert alert-error">An error occurred. Please try again.</div>`;
    })
    .finally(() => {
        button.disabled = false;
        button.textContent = 'Save';
        this.classList.remove('loading');
    });
});

// Handle Password Form
document.getElementById('password-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const button = this.querySelector('.save-button');
    const alertDiv = document.getElementById('password-alert');
    
    button.disabled = true;
    button.textContent = 'Saving...';
    this.classList.add('loading');
    
    fetch('{{ route("settings.update.password") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alertDiv.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
            this.reset();
        } else {
            let errorMessage = data.message;
            if (data.errors) {
                errorMessage += '<ul>';
                Object.values(data.errors).forEach(errors => {
                    errors.forEach(error => {
                        errorMessage += `<li>${error}</li>`;
                    });
                });
                errorMessage += '</ul>';
            }
            alertDiv.innerHTML = `<div class="alert alert-error">${errorMessage}</div>`;
        }
    })
    .catch(error => {
        alertDiv.innerHTML = `<div class="alert alert-error">An error occurred. Please try again.</div>`;
    })
    .finally(() => {
        button.disabled = false;
        button.textContent = 'Save';
        this.classList.remove('loading');
    });
});

// Handle Email Form
document.getElementById('email-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const button = this.querySelector('.save-button');
    const alertDiv = document.getElementById('email-alert');
    
    button.disabled = true;
    button.textContent = 'Saving...';
    this.classList.add('loading');
    
    fetch('{{ route("settings.update.email") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alertDiv.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
            this.reset();
        } else {
            let errorMessage = data.message;
            if (data.errors) {
                errorMessage += '<ul>';
                Object.values(data.errors).forEach(errors => {
                    errors.forEach(error => {
                        errorMessage += `<li>${error}</li>`;
                    });
                });
                errorMessage += '</ul>';
            }
            alertDiv.innerHTML = `<div class="alert alert-error">${errorMessage}</div>`;
        }
    })
    .catch(error => {
        alertDiv.innerHTML = `<div class="alert alert-error">An error occurred. Please try again.</div>`;
    })
    .finally(() => {
        button.disabled = false;
        button.textContent = 'Save';
        this.classList.remove('loading');
    });
});

// Initialize with personal info section open
document.addEventListener('DOMContentLoaded', function() {
    toggleAccordion('personal-info');
});
</script>
@endpush
