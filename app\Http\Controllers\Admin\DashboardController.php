<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Models\BarOrder;
use App\Models\BarEvent;
use Carbon\Carbon;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        // Get merchant ID from authenticated user
        $user = Auth::user();
        $merchantId = $user->id_merchant ?? null;

        // if (!$merchantId) {
        //     // If user doesn't have merchant ID, use fallback or show sample data
        //     $merchantId = null;
        // }

        if (!$merchantId) {
           abort(404);
        }

        if (!$merchantId) {
            return response()->json([
                'success' => false,
                'message' => 'Merchant ID tidak ditemukan. Silakan login ulang.'
            ], 403);
        }

        // Get real statistics from database filtered by merchant
        if ($merchantId) {
            $ticketsSoldToday = BarOrder::getTicketsSoldTodayByMerchant($merchantId);
            $ticketsGrowth = BarOrder::getGrowthPercentageByMerchant($merchantId);
            $totalBookingsToday = BarOrder::getTotalBookingsTodayByMerchant($merchantId);
            $revenueToday = BarOrder::getRevenueTodayByMerchant($merchantId);
        } else {
            // Fallback to general statistics
            $ticketsSoldToday = BarOrder::getTicketsSoldToday();
            $ticketsGrowth = BarOrder::getGrowthPercentage();
            $totalBookingsToday = BarOrder::getTotalBookingsToday();
            $revenueToday = BarOrder::getRevenueToday();
        }

        $stats = [
            'tickets_sold_today' => $ticketsSoldToday,
            'tickets_growth' => $ticketsGrowth,
            'visitors_today' => $ticketsSoldToday, // Sama dengan tiket terjual untuk sementara
            'total_bookings' => $totalBookingsToday,
            'visitors_completed' => $ticketsSoldToday, // Sama dengan tiket terjual untuk sementara
            'estimated_revenue' => $revenueToday
        ];

        // Get upcoming events with pagination (limit 5) filtered by merchant
        if ($merchantId) {
            $upcomingEvents = BarEvent::getUpcomingEventsByMerchant($merchantId, 1, 1);
        } else {
            $upcomingEvents = BarEvent::getUpcomingEvents(1, 1);
        }

        // Get recent completed orders for visitor list filtered by merchant
        $visitorsQuery = BarOrder::completed()->today()->orderBy('created_at', 'desc')->limit(10);

        if ($merchantId) {
            $visitorsQuery->whereHas('event', function($query) use ($merchantId) {
                $query->whereHas('venue', function($venueQuery) use ($merchantId) {
                    $venueQuery->where('id_merchant', $merchantId);
                });
            });
        }

        $visitors = $visitorsQuery->get()
            ->map(function ($order, $index) {
                return [
                    'id' => $index + 1,
                    'name' => $order->full_name,
                    'status' => 'Checked-in'
                ];
            })
            ->toArray();

        return view('admin.dashboard', compact('stats', 'upcomingEvents', 'visitors'));
    }

    public function loadMoreEvents(Request $request)
    {
        $page = $request->get('page', 1);
        $limit = 5;

        // Get merchant ID from authenticated user
        $user = Auth::user();
        $merchantId = $user->id_merchant ?? null;

        if ($merchantId) {
            $events = BarEvent::getUpcomingEventsByMerchant($merchantId, $page, $limit);
        } else {
            $events = BarEvent::getUpcomingEvents($page, $limit);
        }

        // dd($events);

        // Formatkan data untuk JSON
        $formattedEvents = $events->map(fn ($event) => [
                'nama' => $event->nama,
                'formatted_date' => $event->formatted_date,
                'lokasi' => $event->lokasi,
                'image_url' => $event->image_url
            ]);

        return response()->json([
            'success' => true,
            'events' => $formattedEvents,
            'hasMore' => $events->hasMorePages(),
            'currentPage' => $events->currentPage(),
            'lastPage' => $events->lastPage()
        ]);
    }



    public function bookingList(Request $request)
    {
        // Get merchant ID from authenticated user
        $user = Auth::user();
        $merchantId = $user->id_merchant ?? null;

        // For testing: use a default merchant ID if not available
        if (!$merchantId) {
            return response()->json([
                'success' => false,
                'message' => 'Merchant ID tidak ditemukan. Silakan login ulang.'
            ], 403);
        }

        // dd($merchantId);

        // Get filter parameters
        $selectedDate = $request->get('date', Carbon::today()->format('Y-m-d'));
        $selectedLimit = $request->get('limit', '10');
        $searchQuery = $request->get('search', '');
        $selectedVenue = $request->get('venue', 'all');

        // Parse the date - simple approach
        $filterDate = Carbon::today();
        if ($selectedDate !== Carbon::today()->format('Y-m-d')) {
            try {
                $filterDate = Carbon::createFromFormat('Y-m-d', $selectedDate);
            } catch (\Exception $e) {
                $filterDate = Carbon::today();
            }
        }

        // Build query - flexible date matching to handle different formats
        $dateFormatted = $filterDate->format('l, d F Y'); // Monday, 25 December 2023
        $dateShort = $filterDate->format('d F Y'); // 25 December 2023
        $dateYmd = $filterDate->format('Y-m-d'); // 2023-12-25

        $query = BarOrder::where(function($q) use ($dateFormatted, $dateShort, $dateYmd) {
            $q->where('date', $dateFormatted)
              ->orWhere('date', $dateShort)
              ->orWhere('date', $dateYmd)
              ->orWhere('date', 'like', '%' . $dateShort . '%');
        });

        // Debug logging (can be removed in production)
        \Illuminate\Support\Facades\Log::info('Booking List Query Debug', [
            'selected_date' => $selectedDate,
            'date_formatted' => $dateFormatted,
            'date_short' => $dateShort,
            'date_ymd' => $dateYmd,
            'merchant_id' => $merchantId,
            'selected_venue' => $selectedVenue
        ]);

        // Filter by merchant if available
        if ($merchantId) {
            $query->whereHas('venue', function($venueQuery) use ($merchantId) {
                $venueQuery->where('id_merchant', $merchantId);
            });
        }

        // Filter by venue if selected
        if ($selectedVenue && $selectedVenue !== 'all') {
            $query->where('id_venue', $selectedVenue);
        }

        // Add search filter
        if (!empty($searchQuery)) {
            $query->where(function ($q) use ($searchQuery) {
                $q->where('full_name', 'like', '%' . $searchQuery . '%')
                  ->orWhere('phone', 'like', '%' . $searchQuery . '%')
                  ->orWhere('email', 'like', '%' . $searchQuery . '%')
                  ->orWhere('invoice_number', 'like', '%' . $searchQuery . '%')
                  ->orWhere('code_invoice', 'like', '%' . $searchQuery . '%');
            });
        }

        // Get bookings with limit and join with order details for scan status
        $bookings = $query->orderBy('created_at', 'desc')
            ->limit((int)$selectedLimit)
            ->get();

        // Debug logging for results
        \Illuminate\Support\Facades\Log::info('Booking List Results', [
            'total_found' => $bookings->count(),
            'filter_date' => $filterDate->format('Y-m-d'),
            'selected_venue' => $selectedVenue
        ]);

        $bookings = $bookings->map(function ($order) {
                // Get scan status from order details
                $orderDetail = DB::table('tbl_bar_order_detail')
                    ->where('id_order', $order->id)
                    ->first();

                $flagScanned = $orderDetail ? $orderDetail->flag_scanned ?? '0' : '0';
                $qrId = $orderDetail ? $orderDetail->id : $order->id; // Use order detail ID as QR code

                return [
                    'id' => $order->id,
                    'tanggal_kunjungan' => $order->date ? date('l, d F Y', strtotime((string)$order->date)) : '-',
                    'nama_pengunjung' => $order->full_name ?? '-',
                    'qr_id' => $qrId, // Use order detail ID as QR code
                    'nomor_whatsapp' => $order->phone ?? '-',
                    'status_tiket' => $this->getStatusTicketLabel($order->status),
                    'status_tiket_color' => $this->getStatusTicketColor($order->status),
                    'tipe_tiket' => 'FDC Weekday', // Default for now
                    'status_kehadiran' => $this->getStatusKehadiranLabel($flagScanned),
                    'status_kehadiran_color' => $this->getStatusKehadiranColor($flagScanned),
                    'email' => $order->email ?? '-',
                    'status' => $order->status,
                    'check_in_time' => '-', // Will be implemented later
                    'check_out_time' => '-', // Will be implemented later
                ];
            });

        // Get venues for dropdown
        $venues = [];
        if ($merchantId) {
            $venues = DB::table('tbl_bar_venue')
                ->where('id_merchant', $merchantId)
                ->where('flag_aktif', '1')
                ->select('id', 'nama')
                ->orderBy('nama')
                ->get();
        }

        // dd($bookings);

        // Return JSON for AJAX requests
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'bookings' => $bookings->toArray(),
                'venues' => is_array($venues) ? $venues : $venues->toArray(),
                'showing_count' => $bookings->count()
            ]);
        }

        // Return view for regular requests
        $bookings = $bookings->toArray();
        return view('admin.booking-list', compact('bookings', 'selectedDate', 'selectedLimit', 'searchQuery', 'selectedVenue', 'venues'));
    }

    private function getStatusTicketLabel($status)
    {
        switch ($status) {
            case '0':
                return 'Gagal';
            case '1':
                return 'Lunas';
            case '2':
                return 'Batal';
            case '3':
                return 'Menunggu';
            case '4':
                return 'Diproses';
            case '20':
                return 'Expired';
            case '404':
                return 'Hidden';
            default:
                return 'Unknown';
        }
    }

    private function getStatusTicketColor($status)
    {
        switch ($status) {
            case '1': // Selesai/Lunas
                return 'green';
            case '0': // Gagal
            case '2': // Batal
            case '20': // Expired
                return 'red';
            case '3': // Menunggu
            case '4': // Diproses
                return 'yellow';
            case '404': // Hidden
                return 'gray';
            default:
                return 'gray';
        }
    }

    private function getStatusKehadiranLabel($flag_scanned)
    {
        switch ($flag_scanned) {
            case '1':
                return 'Sudah Scan';
            case '0':
            default:
                return 'Belum Scan';
        }
    }

    private function getStatusKehadiranColor($flag_scanned)
    {
        switch ($flag_scanned) {
            case '1': // Sudah scan
                return 'green';
            case '0': // Belum scan
            default:
                return 'gray';
        }
    }
}
