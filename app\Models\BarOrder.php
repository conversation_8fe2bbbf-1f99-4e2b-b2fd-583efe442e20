<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class BarOrder extends Model
{
    protected $table = 'tbl_bar_order';
    
    protected $fillable = [
        'id',
        'id_venue',
        'id_user',
        'id_pg_metod_payget',
        'date',
        'code_invoice',
        'invoice_number',
        'full_name',
        'email',
        'phone',
        'expiry_period',
        'status',
        'payment_method_category',
        'payment_method_name',
        'type_pg',
        'total_qty',
        'disc',
        'total_price',
        'fees',
        'fees_eo',
        'total_amount',
        'currency',
        'currency_value_idr',
        'currency_value_region',
        'region',
        'version',
        'platform',
        'environment',
        'ip_address',
        'lat',
        'lng'
    ];

    protected $casts = [
        // 'date' => 'date', // Don't cast because date is stored as formatted string
        'total_qty' => 'double',
        'disc' => 'double',
        'total_price' => 'double',
        'fees' => 'double',
        'fees_eo' => 'double',
        'total_amount' => 'double',
        'currency_value_idr' => 'double',
        'currency_value_region' => 'double',
        'lat' => 'double',
        'lng' => 'double',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Scope untuk tiket yang sudah selesai (status = '1')
    public function scopeCompleted($query)
    {
        return $query->where('status', '1');
    }

    // Scope untuk hari ini
    public function scopeToday($query)
    {
        $today = Carbon::today();
        return $query->where(function($q) use ($today) {
            $q->where('date', $today->format('l, d F Y'))
              ->orWhere('date', $today->format('d F Y'))
              ->orWhere('date', $today->format('Y-m-d'));
        });
    }

    // Scope untuk kemarin
    public function scopeYesterday($query)
    {
        $yesterday = Carbon::yesterday();
        return $query->where(function($q) use ($yesterday) {
            $q->where('date', $yesterday->format('l, d F Y'))
              ->orWhere('date', $yesterday->format('d F Y'))
              ->orWhere('date', $yesterday->format('Y-m-d'));
        });
    }

    // Relasi dengan venue
    public function venue()
    {
        return $this->belongsTo(BarVenue::class, 'id_venue', 'id');
    }

    // Relasi dengan event melalui venue
    public function event()
    {
        return $this->hasOneThrough(BarEvent::class, BarVenue::class, 'id', 'id_venue', 'id_venue', 'id');
    }

    // Method untuk mendapatkan statistik tiket terjual hari ini
    public static function getTicketsSoldToday()
    {
        return self::completed()->today()->sum('total_qty');
    }

    // Method untuk mendapatkan statistik tiket terjual kemarin
    public static function getTicketsSoldYesterday()
    {
        return self::completed()->yesterday()->sum('total_qty');
    }

    // Method untuk mendapatkan estimasi pendapatan hari ini
    public static function getRevenueToday()
    {
        return self::completed()->today()->sum('total_amount');
    }

    // Method untuk mendapatkan total pemesanan hari ini (semua status)
    public static function getTotalBookingsToday()
    {
        return self::today()->count();
    }

    // Method untuk menghitung growth percentage
    public static function getGrowthPercentage()
    {
        $today = self::getTicketsSoldToday();
        $yesterday = self::getTicketsSoldYesterday();

        if ($yesterday == 0) {
            return $today > 0 ? 100 : 0;
        }

        return round((($today - $yesterday) / $yesterday) * 100, 1);
    }

    // Method untuk mendapatkan statistik berdasarkan merchant
    public static function getTicketsSoldTodayByMerchant($merchantId)
    {
        return self::completed()
            ->today()
            ->whereHas('venue', function($query) use ($merchantId) {
                $query->where('id_merchant', $merchantId);
            })
            ->sum('total_qty');
    }

    public static function getTicketsSoldYesterdayByMerchant($merchantId)
    {
        return self::completed()
            ->yesterday()
            ->whereHas('venue', function($query) use ($merchantId) {
                $query->where('id_merchant', $merchantId);
            })
            ->sum('total_qty');
    }

    public static function getRevenueTodayByMerchant($merchantId)
    {
        return self::completed()
            ->today()
            ->whereHas('venue', function($query) use ($merchantId) {
                $query->where('id_merchant', $merchantId);
            })
            ->sum('total_amount');
    }

    public static function getTotalBookingsTodayByMerchant($merchantId)
    {
        return self::today()
            ->whereHas('venue', function($query) use ($merchantId) {
                $query->where('id_merchant', $merchantId);
            })
            ->count();
    }

    public static function getGrowthPercentageByMerchant($merchantId)
    {
        $today = self::getTicketsSoldTodayByMerchant($merchantId);
        $yesterday = self::getTicketsSoldYesterdayByMerchant($merchantId);

        if ($yesterday == 0) {
            return $today > 0 ? 100 : 0;
        }

        return round((($today - $yesterday) / $yesterday) * 100, 1);
    }
}