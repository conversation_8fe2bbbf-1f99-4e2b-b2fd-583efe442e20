@extends('vip.app')

@section('title', $dj->nama . ' - KREEN VIP')

@push('styles')
    <link href="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/css/lightgallery.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/css/lg-zoom.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/css/lg-thumbnail.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Full width section */
        .full-width-section {
            width: 100vw;
            margin-left: calc(-50vw + 50%);
            padding-left: calc(50vw - 50%);
            padding-right: calc(50vw - 50%);
        }
        /* Hero section styling */
        .hero-section {
            background-size: cover;
            background-position: center;
            position: relative;
            height: 60vh;
            min-height: 400px;
        }
        .hero-section::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8));
            z-index: 1;
        }
        .hero-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        /* Event cards */
        .event-card {
            background: linear-gradient(135deg, rgba(30, 30, 30, 0.9), rgba(50, 50, 50, 0.9));
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 0.75rem;
            padding: 1rem;
            transition: all 0.3s ease;
        }
        .event-card:hover {
            border-color: #D4AF37;
            transform: translateY(-2px);
        }
        .event-image {
            width: 80px;
            height: 80px;
            border-radius: 0.75rem;
            object-fit: cover;
        }
        /* Profile image */
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #D4AF37;
        }
        /* Social media icons */
        .social-icon {
            width: 24px;
            height: 24px;
            fill: #9CA3AF;
            transition: fill 0.3s ease;
        }
        .social-icon:hover {
            fill: #D4AF37;
        }
        /* Gallery grid */
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }
        .gallery-item {
            aspect-ratio: 1;
            border-radius: 0.5rem;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s ease;
            position: relative;
        }
        .gallery-item:hover {
            transform: scale(1.05);
        }
        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        /* Preview gallery styling */
        .preview-gallery-item {
            aspect-ratio: 1;
            border-radius: 0.5rem;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            border: 2px solid transparent;
        }
        .preview-gallery-item:hover {
            transform: scale(1.05);
            border-color: #D4AF37;
            box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
        }
        .preview-gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        /* All Gallery button styling */
        #allGalleryBtn {
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.1), rgba(212, 175, 55, 0.2));
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }
        #allGalleryBtn:hover {
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.3));
            border-color: #D4AF37;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
        }
        /* See All Photos overlay */
        .see-all-overlay {
            position: absolute;
            inset: 0;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            text-align: center;
            transition: background-color 0.3s ease;
        }
        .see-all-overlay:hover {
            background: rgba(0, 0, 0, 0.8);
        }
        /* Live shows */
        .live-show-card {
            background: linear-gradient(135deg, rgba(30, 30, 30, 0.9), rgba(50, 50, 50, 0.9));
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 0.75rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .live-show-card:hover {
            border-color: #D4AF37;
        }
        .live-show-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        /* Mobile responsive adjustments */
        @media (max-width: 1024px) {
            .gallery-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 0.75rem;
            }
        }
        /* Mobile upcoming events positioning */
        .mobile-upcoming-events {
            order: 10;
        }
        @media (max-width: 1024px) {
            .desktop-layout {
                display: flex;
                flex-direction: column;
            }
            .mobile-upcoming-events {
                order: 10;
                margin-top: 3rem;
            }
        }
    </style>
@endpush

@section('content')
    <!-- Hero Section -->
    <section class="full-width-section hero-section"
        style="background-image: url('https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');">
        <div class="hero-content">
            <h2 class="text-white text-2xl md:text-3xl font-medium mb-2">{{ $dj->nama }}</h2>
            <p class="text-gray-300 text-lg">{{ $dj->alamat }}</p>
        </div>
    </section>

    <!-- Main Content -->
    <section class="full-width-section py-12 bg-gradient-to-b from-black to-gray-900">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Desktop Layout - Two Column Layout (8-4 split) -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12 mb-12">
                <!-- Left Column (8 units) - Upcoming Shows, Profile, Gallery -->
                <div class="lg:col-span-2">
                    <!-- Upcoming Shows -->
                    <h3 class="text-white text-xl font-bold mb-6 flex items-center">
                        <span class="text-red-500 mr-2">🔥</span>
                        Upcoming Shows
                    </h3>
                    <div class="space-y-6">
                        @foreach ($upcomingEvents as $upcomingEvent)
                            <a href="{{ route('event.detailEvent', $upcomingEvent->slug) }}" class="event-card block bg-gray-800 border border-yellow-500/30 rounded-xl p-4 hover:border-yellow-400 transition-all">
                                <div class="flex items-center space-x-4">
                                    <img src="https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80"
                                        alt="{{ $upcomingEvent->nama }}" class="w-20 h-20 rounded-lg object-cover">
                                    <div class="flex-1">
                                        <h4 class="text-white font-medium text-lg mb-1">{{ $upcomingEvent->nama }}</h4>
                                        <p class="text-gray-400 text-sm">{{ $upcomingEvent->venue_name }}</p>
                                        <p class="text-gray-400 text-sm">{{ $upcomingEvent->tanggal }}</p>
                                    </div>
                                </div>
                            </a>
                        @endforeach
                    </div>

                    <!-- Profile Section -->
                    <div class="mt-12">
                        <h3 class="text-white text-xl font-bold mb-6">Profile</h3>
                        <div class="flex flex-col space-y-6">
                            <div class="w-full">
                                <img src="{{ strpos($dj->gambar, 'http') === 0 ? $dj->gambar : asset($dj->gambar) }}"
                                    alt="{{ $dj->nama }} Profile" class="w-full h-80 object-cover rounded-lg"> {{-- Adjusted height to h-80 --}}
                                <h4 class="text-white text-2xl font-bold mt-4 mb-2">{{ $dj->nama }}</h4>
                            </div>
                            <div class="w-full text-gray-300 text-base leading-relaxed space-y-4">
                                {!! $dj->deskripsi !!}
                            </div>
                        </div>
                    </div>

                    <!-- Gallery Section -->
                    <div class="mt-12">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-white text-xl font-bold">Gallery</h3>
                            @if(count($galleries) > 2)
                                <button id="allGalleryBtn" class="text-yellow-400 hover:text-yellow-300 text-sm font-medium transition-colors duration-300 flex items-center space-x-1">
                                    <span>All Gallery ({{ count($galleries) }})</span>
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </button>
                            @endif
                        </div>
                        <!-- Preview Gallery (Max 2 items) -->
                        <div id="previewGallery" class="grid grid-cols-2 gap-4 mb-4">
                            @foreach ($galleries->take(2) as $gallery)
                                <div class="overflow-hidden rounded-lg cursor-pointer preview-gallery-item"
                                     data-src="{{ strpos($gallery->gambar, 'http') === 0 ? $gallery->gambar : asset($gallery->gambar) }}"
                                     data-sub-html="<h4>DJ Gallery</h4>">
                                    <img src="{{ strpos($gallery->gambar, 'http') === 0 ? $gallery->gambar : asset($gallery->gambar) }}"
                                         alt="Gallery" class="w-full h-40 object-cover transition-transform hover:scale-105"> {{-- Adjusted height to h-40 --}}
                                </div>
                            @endforeach
                        </div>
                        <!-- Hidden Full Gallery for Light Gallery -->
                        <div id="fullGallery" class="hidden">
                            @foreach ($galleries as $gallery)
                                <div class="full-gallery-item"
                                     data-src="{{ strpos($gallery->gambar, 'http') === 0 ? $gallery->gambar : asset($gallery->gambar) }}"
                                     data-sub-html="<h4>DJ Gallery</h4>">
                                    <img src="{{ strpos($gallery->gambar, 'http') === 0 ? $gallery->gambar : asset($gallery->gambar) }}"
                                         alt="Gallery" class="w-full h-48 object-cover">
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Right Column (4 units) - Social Media & Streaming Links -->
                <div class="space-y-8 lg:col-span-1">
                    <!-- Social Media -->
                    <div>
                        <h3 class="text-white text-xl font-bold mb-6">Social Media</h3>
                        <div class="space-y-4">
                            <p class="text-sm text-gray-400 flex items-center space-x-2">
                                <i class="fab fa-instagram text-pink-500 w-5"></i>
                                <span>Instagram: {{ $dj->ig }}</span>
                            </p>
                            <p class="text-sm text-gray-400 flex items-center space-x-2">
                                <i class="fab fa-facebook text-blue-500 w-5"></i>
                                <span>Facebook: {{ $dj->fb }}</span>
                            </p>
                        </div>
                    </div>
                    <!-- Streaming Links -->
                    <div>
                        <h3 class="text-white text-xl font-bold mb-6">Streaming Links</h3>
                        <div class="space-y-4">
                            <p class="text-sm text-gray-400 flex items-center space-x-2">
                                <i class="fab fa-spotify text-green-500 w-5"></i>
                                <span>Spotify: {{ $dj->spotify }}</span>
                            </p>
                            <p class="text-sm text-gray-400 flex items-center space-x-2">
                                <i class="fab fa-youtube text-red-500 w-5"></i>
                                <span>YouTube: {{ $dj->yt }}</span>
                            </p>
                            <p class="text-sm text-gray-400 flex items-center space-x-2">
                                <i class="fas fa-music text-purple-400 w-5"></i>
                                <span>YT Music: {{ $dj->yt_music }}</span>
                            </p>
                            <p class="text-sm text-gray-400 flex items-center space-x-2">
                                <i class="fab fa-soundcloud text-orange-400 w-5"></i>
                                <span>SoundCloud: {{ $dj->soundcloud }}</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Live Shows Section (remains full width below the 8-4 grid) -->
            <div class="mb-12 hidden md:block">
                <h3 class="text-white text-xl font-bold mb-6">Live Shows</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    @foreach ($pastEvents as $pastEvent)
                        <div class="bg-gray-800 rounded-xl overflow-hidden border border-yellow-500/30 hover:border-yellow-400">
                            <img src="https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                                alt="Live Show" class="w-full h-32 object-cover">
                            <div class="p-4">
                                <h4 class="text-white font-medium text-sm mb-1">{{ $pastEvent->nama }}</h4>
                                <p class="text-gray-400 text-xs mb-2">{{ $pastEvent->venue_name }} • {{ $pastEvent->tanggal }}</p>
                                <p class="text-gray-300 text-xs">Experience the energy with {{ $dj->nama }} on stage!</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Mobile Upcoming Events Section (Appears at bottom on mobile) -->
            <div class="mobile-upcoming-events lg:hidden">
                <h3 class="text-white text-xl font-bold mb-6 flex items-center">
                    <span class="text-red-500 mr-2">🔥</span>
                    Upcoming Shows
                </h3>
                <div class="space-y-6">
                    @foreach ($upcomingEvents as $upcomingEvent)
                        <a href="{{ route('event.detailEvent', $upcomingEvent->slug) }}" class="event-card">
                            <div class="flex items-center space-x-4">
                                <img src="https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80"
                                    alt="Princess Joana & DJ Yasmin" class="event-image">
                                <div class="flex-1">
                                    <h4 class="text-white font-medium text-lg mb-1">{{ $upcomingEvent->nama }}</h4>
                                    <p class="text-gray-400 text-sm">{{ $upcomingEvent->venue_name }}</p>
                                    <p class="text-gray-400 text-sm">{{ $upcomingEvent->tanggal }}</p>
                                </div>
                            </div>
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/lightgallery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/plugins/zoom/lg-zoom.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/plugins/thumbnail/lg-thumbnail.min.js"></script>
    <!-- Gallery Data -->
    <script>
        window.galleryData = [
            @foreach ($galleries as $index => $gallery)
            {
                src: '{{ strpos($gallery->gambar, "http") === 0 ? $gallery->gambar : asset($gallery->gambar) }}',
                thumb: '{{ strpos($gallery->gambar, "http") === 0 ? $gallery->gambar : asset($gallery->gambar) }}',
                subHtml: '<h4>DJ Gallery {{ $index + 1 }}</h4>'
            }{{ $loop->last ? '' : ',' }}
            @endforeach
        ];
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            let fullGalleryInstance = null;
            // Use gallery data from window object
            const galleryData = window.galleryData || [];

            // Initialize Full Gallery with dynamic data
            function initFullGallery() {
                const fullGalleryElement = document.getElementById('fullGallery');
                if (fullGalleryElement && galleryData.length > 0) {
                    fullGalleryInstance = lightGallery(fullGalleryElement, {
                        plugins: [lgZoom, lgThumbnail],
                        speed: 500,
                        thumbnail: true,
                        animateThumb: false,
                        zoomFromOrigin: false,
                        allowMediaOverlap: true,
                        toggleThumb: true,
                        dynamic: true,
                        dynamicEl: galleryData
                    });
                }
            }
            // Initialize full gallery
            initFullGallery();

            // Handle "All Gallery" button click
            const allGalleryBtn = document.getElementById('allGalleryBtn');
            if (allGalleryBtn) {
                allGalleryBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    // Open full gallery starting from first image
                    if (fullGalleryInstance) {
                        fullGalleryInstance.openGallery(0);
                    }
                });
            }

            // Add click handlers for preview gallery items
            const previewItems = document.querySelectorAll('.preview-gallery-item');
            previewItems.forEach((item, index) => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    // Open full gallery starting from clicked image index
                    if (fullGalleryInstance) {
                        fullGalleryInstance.openGallery(index);
                    }
                });
            });
        });
    </script>
@endpush
