<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        $url = $request->url();

        // Cek apakah user sudah login
        if (Auth::check()) {
            $user = Auth::user();

            Log::info('AdminMiddleware: User accessing admin area', [
                'user_id' => $user->id,
                'email' => $user->email,
                'role' => $user->role,
                'url' => $url
            ]);

            // Cek apakah role user adalah 'admin'
            if ($user->role === 'admin') {
                Log::info('AdminMiddleware: Admin access granted');
                return $next($request);
            } else {
                Log::warning('AdminMiddleware: Non-admin user trying to access admin area', [
                    'user_role' => $user->role,
                    'redirecting_to' => 'home'
                ]);
                // Jika user biasa mencoba akses admin, redirect ke home
                return redirect()->route('home')->with('error', 'You do not have permission to access admin area.');
            }
        }

        Log::info('AdminMiddleware: Unauthenticated user trying to access admin area', [
            'url' => $url,
            'redirecting_to' => 'login'
        ]);

        // Jika belum login, arahkan ke halaman login
        return redirect()->route('auth.login');
    }
}
