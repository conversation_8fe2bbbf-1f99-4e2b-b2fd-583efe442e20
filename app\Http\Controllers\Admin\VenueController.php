<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class VenueController extends Controller
{
    public function index(Request $request)
    {
        // Get merchant ID from authenticated user
        $user = Auth::user();
        $merchantId = $user->id_merchant ?? null;

        // dd($merchantId);

        // For testing: use a default merchant ID if not available
        if (!$merchantId) {
            // $merchantId = '1'; // Default merchant ID for testing
            abort(404);

        }

        // if (!$merchantId) {
        //     return response()->json([
        //         'success' => false,
        //         'message' => 'Merchant ID tidak ditemukan. Silakan login ulang.'
        //     ], 403);
        // }
        
        // Get filter parameters
        $search = $request->input('search', '');
        $status = $request->input('status', '');
        $page = $request->input('page', 1);
        $perPage = 10;

        // Build query
        $query = DB::table('tbl_bar_venue as v')
            ->select(
                'v.*'
            );
            
        // Filter by merchant if available
        if ($merchantId) {
            $query->where('v.id_merchant', $merchantId);
        }

        // Add search filter
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('v.nama', 'like', "%{$search}%")
                  ->orWhere('v.alamat', 'like', "%{$search}%")
                  ->orWhere('v.informasi', 'like', "%{$search}%");
            });
        }

        // Add status filter
        if (!empty($status) && $status !== 'all') {
            $query->where('v.flag_aktif', $status);
        }

        // Get total count for pagination
        $totalCount = $query->count();

        // Get venues with pagination
        $offset = ($page - 1) * $perPage;
        $venues = $query->orderBy('v.created_at', 'desc')
            ->offset($offset)
            ->limit($perPage)
            ->get()
            ->map(function ($venue) {
                return [
                    'id' => $venue->id,
                    'nama' => $venue->nama ?? '-',
                    'alamat' => $venue->alamat ?? '-',
                    'informasi' => $venue->informasi ?? '-', // Full text for edit form
                    'informasi_display' => $venue->informasi ? Str::limit($venue->informasi, 100) : '-', // Truncated for table display
                    'status' => $this->getStatusLabel($venue->flag_aktif),
                    'status_color' => $this->getStatusColor($venue->flag_aktif),
                    'banner' => $venue->banner ?? null,
                    'currency' => $venue->currency ?? 'IDR',
                    'lat' => $venue->lat,
                    'lng' => $venue->lng,
                    'id_negara' => $venue->id_negara,
                    'id_propinsi' => $venue->id_propinsi,
                    'id_kota' => $venue->id_kota,
                    'id_kelurahan' => $venue->id_kelurahan,
                    'slug' => $venue->slug,
                    'flag_aktif' => $venue->flag_aktif,
                    'username_ig' => $venue->username_ig,
                    'no_wa' => $venue->no_wa,
                    'created_at' => $venue->created_at ? Carbon::parse($venue->created_at)->format('d M Y H:i') : '-'
                ];
            });

        // Get provinces for dropdown
        $provinces = DB::table('kreen_production_online.tbl_adm_province')
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        // Calculate pagination info
        $hasMore = $totalCount > ($page * $perPage);
        $currentCount = $venues->count();
        $showingFrom = $offset + 1;
        $showingTo = $offset + $currentCount;

        // Return JSON for AJAX requests
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'venues' => $venues->toArray(),
                'provinces' => $provinces->toArray(),
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total_count' => $totalCount,
                    'current_count' => $currentCount,
                    'showing_from' => $showingFrom,
                    'showing_to' => $showingTo,
                    'has_more' => $hasMore
                ]
            ]);
        }

        // Return view for regular requests
        $venues = $venues->toArray();
        $provinces = $provinces->toArray();
        return view('admin.venues', compact('venues', 'provinces', 'search', 'status'));
    }

    public function getOpenHours($venueId)
    {
        $openHours = DB::table('tbl_bar_venue_open_hour')
            ->where('id_venue', $venueId)
            ->orderBy('hari')
            ->get()
            ->map(function ($hour) {
                return [
                    'id' => $hour->id,
                    'hari' => $this->convertNumberToDay($hour->hari),
                    'jam_buka' => $hour->jam_buka,
                    'jam_tutup' => $hour->jam_tutup
                ];
            });

        return response()->json([
            'success' => true,
            'open_hours' => $openHours
        ]);
    }

    public function getTicketTypes($venueId)
    {
        $ticketTypes = DB::table('tbl_bar_jenis_tiket')
            ->where('id_venue', $venueId)
            ->orderBy('created_at')
            ->get()
            ->map(function ($ticket) {
                return [
                    'id' => $ticket->id,
                    'nama_tiket' => $ticket->nama_tiket,
                    'note_tiket' => $ticket->note_tiket,
                    'default_harga' => $ticket->default_harga,
                    'default_stok' => $ticket->default_stok,
                    'flag_aktif' => $ticket->flag_aktif
                ];
            });

        return response()->json([
            'success' => true,
            'ticket_types' => $ticketTypes
        ]);
    }

    public function getProvinces()
    {
        $provinces = DB::table('kreen_production_online.tbl_adm_province')
            ->select('id', 'name')
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'provinces' => $provinces
        ]);
    }

    public function getRegencies(Request $request)
    {
        $provinceId = $request->input('province_id');

        $regencies = DB::table('kreen_production_online.tbl_adm_regency')
            ->select('id', 'name')
            ->where('id_province', $provinceId)
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'regencies' => $regencies
        ]);
    }

    public function getDistricts(Request $request)
    {
        $regencyId = $request->input('regency_id');

        $districts = DB::table('kreen_production_online.tbl_adm_district')
            ->select('id', 'name')
            ->where('id_regency', $regencyId)
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'districts' => $districts
        ]);
    }
    
    public function store(Request $request)
    {
        try {
            // Debug: Log all request data
            Log::info('=== VENUE STORE REQUEST ===');
            Log::info('All request data:', $request->all());
            Log::info('Files:', $request->allFiles());

            // Get merchant ID from authenticated user
            $user = Auth::user();
            $merchantId = $user->id_merchant ?? null;
            
            if (!$merchantId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Merchant ID tidak ditemukan. Silakan login ulang.'
                ], 403);
            }
            
            $request->validate([
                'nama' => 'required|string|max:255',
                'informasi' => 'nullable|string',
                'alamat' => 'required|string|max:500',
                'currency' => 'required|string|max:10',
                'lat' => 'nullable|numeric',
                'lng' => 'nullable|numeric',
                'id_negara' => 'nullable|string|max:20',
                'id_propinsi' => 'nullable|string|max:20',
                'id_kota' => 'nullable|string|max:20',
                'id_kelurahan' => 'nullable|string|max:20',
                'flag_aktif' => 'required|in:0,1',
                'username_ig' => 'nullable|string|max:100',
                'no_wa' => 'nullable|string|max:20',
                'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'galleries.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'open_hours' => 'nullable|array',
                'open_hours.*.hari' => 'nullable|string|max:20',
                'open_hours.*.jam_buka' => 'nullable|string|max:10',
                'open_hours.*.jam_tutup' => 'nullable|string|max:10',
                'ticket_types' => 'required|array|min:1',
                'ticket_types.*.nama_tiket' => 'required|string|max:255',
                'ticket_types.*.note_tiket' => 'nullable|string',
                'ticket_types.*.default_harga' => 'required|numeric|min:0',
                'ticket_types.*.default_stok' => 'required|integer|min:1'
            ]);

            $venueId = (int) substr(str_replace('.', '', microtime(true)), 0, 10);
            $slug = Str::slug($request->nama . '-' . rand(0, 9999));

            // Handle banner upload
            $bannerPath = null;
            if ($request->hasFile('banner')) {
                $banner = $request->file('banner');
                $bannerName = time() . '_banner_' . $banner->getClientOriginalExtension();
                $banner->move(public_path('uploads/venues'), $bannerName);
                $bannerPath = '/uploads/venues/' . $bannerName;
            }

            // Insert venue
            DB::table('tbl_bar_venue')->insert([
                'id' => $venueId,
                'id_merchant' => $merchantId,
                'nama' => $request->nama,
                'informasi' => $request->informasi,
                'banner' => $bannerPath,
                'currency' => $request->currency,
                'lat' => $request->lat,
                'lng' => $request->lng,
                'id_negara' => $request->id_negara,
                'id_propinsi' => $request->id_propinsi,
                'id_kota' => $request->id_kota,
                'id_kelurahan' => $request->id_kelurahan,
                'alamat' => $request->alamat,
                'slug' => $slug,
                'flag_aktif' => $request->flag_aktif,
                'username_ig' => $request->username_ig,
                'no_wa' => $request->no_wa,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Handle gallery uploads
            if ($request->hasFile('galleries')) {
                foreach ($request->file('galleries') as $index => $gallery) {
                    $galleryName = time() . '_gallery_' . $index . '_' . $gallery->getClientOriginalExtension();
                    $gallery->move(public_path('uploads/venues/galleries'), $galleryName);
                    
                    DB::table('tbl_bar_venue_galeri')->insert([
                        'id' => strval(substr(str_replace('.', '', microtime(true)), 0, 10) . $index),
                        'id_venue' => $venueId,
                        'gambar' => '/uploads/venues/galleries/' . $galleryName,
                        'flag_aktif' => '1',
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }

            // Handle open hours using the same logic as update
            if ($request->has('open_hours') && is_array($request->open_hours)) {
                $this->updateOpenHours($venueId, $request->open_hours);
            } else {
                Log::info('No open_hours data provided for new venue');
            }

            // Create ticket types from form input
            if ($request->has('ticket_types') && is_array($request->ticket_types)) {
                $this->createTicketTypes($venueId, $request->ticket_types);
            }

            return response()->json([
                'success' => true,
                'message' => 'Venue berhasil ditambahkan!'
            ]);

        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    private function getStatusLabel($status)
    {
        return $status == '1' ? 'Active' : 'Inactive';
    }

    private function getStatusColor($status)
    {
        return $status == '1' ? 'green' : 'red';
    }

    private function convertDayToNumber($dayName)
    {
        $days = [
            'Monday' => 1,
            'Tuesday' => 2,
            'Wednesday' => 3,
            'Thursday' => 4,
            'Friday' => 5,
            'Saturday' => 6,
            'Sunday' => 7
        ];

        return $days[$dayName] ?? null;
    }

    private function convertNumberToDay($dayNumber)
    {
        $days = [
            1 => 'Monday',
            2 => 'Tuesday',
            3 => 'Wednesday',
            4 => 'Thursday',
            5 => 'Friday',
            6 => 'Saturday',
            7 => 'Sunday'
        ];

        return $days[$dayNumber] ?? null;
    }

    private function createTicketTypes($venueId, $ticketTypes)
    {
        foreach ($ticketTypes as $ticketType) {
            if (!empty($ticketType['nama_tiket']) && !empty($ticketType['default_harga']) && !empty($ticketType['default_stok'])) {
                DB::table('tbl_bar_jenis_tiket')->insert([
                    'id' => (int) substr(str_replace('.', '', microtime(true)), 0, 10),
                    'id_venue' => $venueId,
                    'nama_tiket' => $ticketType['nama_tiket'],
                    'note_tiket' => $ticketType['note_tiket'] ?? '',
                    'default_harga' => $ticketType['default_harga'],
                    'default_stok' => $ticketType['default_stok'],
                    'total_terjual' => 0,
                    'stok_tersedia' => $ticketType['default_stok'],
                    'flag_aktif' => '1',
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        }

        Log::info("Created ticket types for venue: {$venueId}", ['ticket_types' => $ticketTypes]);
    }

    public function update(Request $request, $id)
    {
        try {
            // Get merchant ID from authenticated user
            $user = Auth::user();
            $merchantId = $user->id_merchant ?? null;

            if (!$merchantId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Merchant ID tidak ditemukan. Silakan login ulang.'
                ], 403);
            }

            // Check if venue belongs to merchant
            $venue = DB::table('tbl_bar_venue')
                ->where('id', $id)
                ->where('id_merchant', $merchantId)
                ->first();

            if (!$venue) {
                return response()->json([
                    'success' => false,
                    'message' => 'Venue tidak ditemukan atau Anda tidak memiliki akses.'
                ], 404);
            }

            $request->validate([
                'nama' => 'required|string|max:255',
                'informasi' => 'nullable|string',
                'alamat' => 'required|string|max:500',
                'currency' => 'required|string|max:10',
                'lat' => 'nullable|numeric',
                'lng' => 'nullable|numeric',
                'id_negara' => 'nullable|string|max:20',
                'id_propinsi' => 'nullable|string|max:20',
                'id_kota' => 'nullable|string|max:20',
                'id_kelurahan' => 'nullable|string|max:20',
                'flag_aktif' => 'required|in:0,1',
                'username_ig' => 'nullable|string|max:100',
                'no_wa' => 'nullable|string|max:20',
                'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'galleries.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'open_hours.*.hari' => 'nullable|string|max:20',
                'open_hours.*.jam_buka' => 'nullable|string|max:10',
                'open_hours.*.jam_tutup' => 'nullable|string|max:10',
                'ticket_types' => 'required|array|min:1',
                'ticket_types.*.nama_tiket' => 'required|string|max:255',
                'ticket_types.*.note_tiket' => 'nullable|string',
                'ticket_types.*.default_harga' => 'required|numeric|min:0',
                'ticket_types.*.default_stok' => 'required|integer|min:1'
            ]);

            // $slug = Str::slug($request->nama . '-' . rand(0, 9999));

            $slug = $venue->slug;

            // Jika nama berubah → update slug
            if ($request->nama !== $venue->nama) {
                $slug = Str::slug($request->nama . '-' . rand(0, 9999));
            }

            // Handle banner upload
            $bannerPath = $venue->banner; // Keep existing banner
            if ($request->hasFile('banner')) {
                $banner = $request->file('banner');
                $bannerName = time() . '_banner_' . $banner->getClientOriginalExtension();
                $banner->move(public_path('uploads/venues'), $bannerName);
                $bannerPath = '/uploads/venues/' . $bannerName;
            }

            // Update venue
            DB::table('tbl_bar_venue')->where('id', $id)->update([
                'nama' => $request->nama,
                'informasi' => $request->informasi,
                'banner' => $bannerPath,
                'currency' => $request->currency,
                'lat' => $request->lat,
                'lng' => $request->lng,
                'id_negara' => $request->id_negara,
                'id_propinsi' => $request->id_propinsi,
                'id_kota' => $request->id_kota,
                'id_kelurahan' => $request->id_kelurahan,
                'alamat' => $request->alamat,
                'slug' => $slug,
                'flag_aktif' => $request->flag_aktif,
                'username_ig' => $request->username_ig,
                'no_wa' => $request->no_wa,
                'updated_at' => now()
            ]);

            // Handle gallery uploads
            if ($request->hasFile('galleries')) {
                foreach ($request->file('galleries') as $index => $gallery) {
                    $galleryName = time() . '_gallery_' . $index . '_' . $gallery->getClientOriginalExtension();
                    $gallery->move(public_path('uploads/venues/galleries'), $galleryName);

                    DB::table('tbl_bar_venue_galeri')->insert([
                        'id' => strval(substr(str_replace('.', '', microtime(true)), 0, 10) . $index),
                        'id_venue' => $id,
                        'gambar' => '/uploads/venues/galleries/' . $galleryName,
                        'flag_aktif' => '1',
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }

            // Update open hours - smart update/insert
            if ($request->has('open_hours') && is_array($request->open_hours)) {
                $this->updateOpenHours($id, $request->open_hours);
            }

            // Update ticket types - smart update/insert
            if ($request->has('ticket_types') && is_array($request->ticket_types)) {
                $this->updateTicketTypes($id, $request->ticket_types);
            }

            return response()->json([
                'success' => true,
                'message' => 'Venue berhasil diupdate!'
            ]);

        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    /**
     * Update open hours efficiently - update existing, insert new, delete removed
     */
    private function updateOpenHours($venueId, $openHours)
    {
        Log::info("Starting updateOpenHours for venue {$venueId}", ['open_hours_count' => count($openHours)]);

        // First, delete all existing open hours for this venue to avoid duplicates
        DB::table('tbl_bar_venue_open_hour')
            ->where('id_venue', $venueId)
            ->delete();

        Log::info("Deleted existing open hours for venue {$venueId}");

        $processedDays = []; // Track processed days to prevent duplicates
        $insertedCount = 0;

        foreach ($openHours as $index => $openHour) {
            if (is_array($openHour) && isset($openHour['hari']) && isset($openHour['jam_buka']) && isset($openHour['jam_tutup'])) {
                if (!empty($openHour['hari'])) {
                    $dayNumber = $this->convertDayToNumber($openHour['hari']);

                    if ($dayNumber) {
                        // Check for duplicate days in current request
                        if (in_array($dayNumber, $processedDays)) {
                            Log::warning("Duplicate day detected in request: {$openHour['hari']} (day number: {$dayNumber}) for venue {$venueId}");
                            continue; // Skip this duplicate day
                        }

                        $processedDays[] = $dayNumber; // Mark this day as processed

                        // Generate unique ID using multiple factors
                        $uniqueId = $this->generateUniqueOpenHourId($venueId, $dayNumber, $index);

                        $data = [
                            'id' => $uniqueId,
                            'id_venue' => $venueId,
                            'hari' => $dayNumber,
                            'jam_buka' => $openHour['jam_buka'] ?? '00:00',
                            'jam_tutup' => $openHour['jam_tutup'] ?? '23:59',
                            'created_at' => now(),
                            'updated_at' => now()
                        ];

                        try {
                            DB::table('tbl_bar_venue_open_hour')->insert($data);
                            $insertedCount++;
                            Log::info("Inserted open hour for venue {$venueId}: day {$dayNumber} ({$openHour['hari']}) with ID {$uniqueId}");
                        } catch (\Exception $e) {
                            Log::error("Failed to insert open hour for venue {$venueId}: " . $e->getMessage(), [
                                'data' => $data,
                                'error' => $e->getMessage()
                            ]);
                            // Continue with next open hour instead of failing completely
                        }
                    } else {
                        Log::warning("Invalid day name: {$openHour['hari']} for venue {$venueId}");
                    }
                } else {
                    Log::info("Empty day field, skipping open hour at index {$index} for venue {$venueId}");
                }
            } else {
                Log::warning("Invalid open hour data structure at index {$index} for venue {$venueId}", ['data' => $openHour]);
            }
        }

        Log::info("Completed updateOpenHours for venue {$venueId}: inserted {$insertedCount} open hours");
    }

    /**
     * Generate unique ID for open hour using multiple factors
     */
    private function generateUniqueOpenHourId($venueId, $dayNumber, $index)
    {
        // Use combination of timestamp, venue ID, day number, and index to ensure uniqueness
        $timestamp = (int) (microtime(true) * 1000); // Include milliseconds
        $hash = crc32($venueId . $dayNumber . $index . $timestamp);

        // Ensure positive integer and reasonable length
        $uniqueId = abs($hash) % 2147483647; // Max int value for MySQL INT

        // Add additional uniqueness if needed
        if ($uniqueId < 1000000) {
            $uniqueId += 1000000; // Ensure minimum 7 digits
        }

        Log::info("Generated unique ID {$uniqueId} for venue {$venueId}, day {$dayNumber}, index {$index}");

        return $uniqueId;
    }

    /**
     * Update ticket types efficiently - update existing, insert new, delete removed
     */
    private function updateTicketTypes($venueId, $ticketTypes)
    {
        // Get existing ticket types
        $existingTickets = DB::table('tbl_bar_jenis_tiket')
            ->where('id_venue', $venueId)
            ->get()
            ->keyBy('id');

        $processedIds = [];

        foreach ($ticketTypes as $ticketType) {
            if (!empty($ticketType['nama_tiket']) && !empty($ticketType['default_harga']) && !empty($ticketType['default_stok'])) {
                $data = [
                    'nama_tiket' => $ticketType['nama_tiket'],
                    'note_tiket' => $ticketType['note_tiket'] ?? '',
                    'default_harga' => $ticketType['default_harga'],
                    'default_stok' => $ticketType['default_stok'],
                    'flag_aktif' => $ticketType['flag_aktif'] ?? '1',
                    'updated_at' => now()
                ];

                if (isset($ticketType['id']) && $existingTickets->has($ticketType['id'])) {
                    // Update existing ticket type
                    DB::table('tbl_bar_jenis_tiket')
                        ->where('id', $ticketType['id'])
                        ->where('id_venue', $venueId)
                        ->update($data);
                    $processedIds[] = $ticketType['id'];
                } else {
                    // Insert new ticket type
                    $data['id'] = (int) substr(str_replace('.', '', microtime(true)), 0, 10);
                    $data['id_venue'] = $venueId;
                    $data['total_terjual'] = 0;
                    $data['stok_tersedia'] = $ticketType['default_stok'];
                    $data['created_at'] = now();

                    DB::table('tbl_bar_jenis_tiket')->insert($data);
                    $processedIds[] = $data['id'];
                }
            }
        }

        // Soft delete ticket types that were not processed (removed from frontend)
        if (!empty($processedIds)) {
            DB::table('tbl_bar_jenis_tiket')
                ->where('id_venue', $venueId)
                ->whereNotIn('id', $processedIds)
                ->update(['flag_aktif' => '0', 'updated_at' => now()]);
        }
    }
}
