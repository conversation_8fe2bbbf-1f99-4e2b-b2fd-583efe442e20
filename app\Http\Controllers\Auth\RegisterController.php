<?php

namespace App\Http\Controllers\Auth;

use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Validator;
use Illuminate\Foundation\Auth\RegistersUsers;

class RegisterController extends Controller
{

    public function register()
    {
        return view('auth.register');
    }

    public function submitRegister(Request $request)
    {
        $data = $request->validate([
            'email' => ['required', 'string', 'email', 'max:255', 'unique:tbl_users'],
            'gender' => ['required', 'string', 'in:male,female'],
            'name' => ['required', 'string', 'max:255'],
            'phone' => ['required', 'string', 'max:255'],
            'password' => ['required', 'string', 'max:255', 'regex:/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d\W_]{8,}$/'],
            'role' => ['nullable', 'string', 'in:user,admin'],
        ]);

        User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'gender' => $data['gender'],
            'phone' => $data['phone'],
            'password' => Hash::make($data['password']),
            'role' => $data['role'] ?? 'user',
            'status' => "1",
            'created_at' => now(),
        ]);

        return to_route('auth.login')->with('success', 'Registration successful!');
    }
}
