name: 🚀 Deploy Website on Push

on:
  push:
    branches:
      - main
      - dev

jobs:

  deploy-production:
    name: 🎉 Deploy to Production Kreenvip
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Deploy using ssh
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.HOSTNAME }}
        username: ${{ secrets.USERNAME }}
        password: ${{ secrets.PASSWORD }}
        port: ${{ secrets.PORT }}
        script: |
          cd public_html
          git fetch
          git pull origin main
          composer install
          export NVM_DIR="$HOME/.nvm"
          [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
          npm run build

  deploy-dev:
    name: 🎉 Deploy to Dev Kreenvip
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/dev'
    steps:
      - name: Deploy using SSH
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOSTNAME }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          port: ${{ secrets.PORT }}
          script: |
            cd domains/dev.kreenvip.com/public_html
            git fetch
            git pull origin dev
            composer install
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
            npm run build
