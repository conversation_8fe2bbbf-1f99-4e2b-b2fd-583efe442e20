@extends('vip.app')

@section('title', 'Payment - KREEN VIP')

@push('styles')
    <style>
        .sticky-card {
            position: sticky;
            top: 10rem;
            z-index: 10;
        }

        .booking-container {
            position: relative;
            overflow: visible;
        }

        .booking-grid {
            position: relative;
            overflow: visible;
        }

        /* Full width section */
        .full-width-section {
            width: 100vw;
            margin-left: calc(-50vw + 50%);
            padding-left: calc(50vw - 50%);
            padding-right: calc(50vw - 50%);
        }

        /* Header with background image */
        .header-bg {
            background-image: url('https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .header-bg::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
            z-index: 1;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        /* List style */
        ul,
        ol {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        ul>li:not(:last-child),
        ol>li:not(:last-child) {
            /* border-bottom: 1px solid #e2e8f0; */
        }

        ul>li,
        ol>li {
            padding: 0.75rem 0;
            counter-increment: list-counter;
        }

        ol>li::before {
            content: counter(list-counter) ". ";
            color: var(--color-kreen-gold);
            font-weight: 600;
            margin-right: 0.5rem;
        }


        /* Mobile sticky adjustments */
        @media (max-width: 1024px) {
            .sticky-card {
                position: relative;
                top: auto;
            }
        }

        /* Progress indicator responsive design */
        @media (max-width: 768px) {
            .booking-progress {
                display: none !important;
            }

            .booking-progress-mobile {
                display: flex !important;
                justify-content: center;
                padding: 0.5rem 0;
                border-top: 1px solid #374151;
                margin-top: 1rem;
            }
        }

        @media (min-width: 769px) {
            .booking-progress-mobile {
                display: none !important;
            }
        }

        /* Timer animation */
        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.7;
            }
        }

        .timer-pulse {
            animation: pulse 2s infinite;
        }

        /* Copy button hover effect */
        .copy-btn:hover {
            background-color: rgba(163, 122, 29, 0.1);
        }

        /* Payment Overlay Fix */
        #payment-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background-color: rgba(0, 0, 0, 0.8) !important;
            backdrop-filter: blur(4px) !important;
            z-index: 9999 !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
        }

        #payment-overlay.hidden {
            display: none !important;
        }

        #payment-overlay .loading-content {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        #payment-overlay .loading-spinner {
            width: 3rem;
            height: 3rem;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #D4AF37;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        #payment-modal {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background-color: rgba(0, 0, 0, 0.7) !important;
            backdrop-filter: blur(4px) !important;
            z-index: 9998 !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
        }

        #payment-modal.hidden {
            display: none !important;
        }

        #payment-modal .modal-content {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            max-width: 90%;
            width: 100%;
            max-width: 420px;
            box-shadow: 0 0 25px rgba(255, 255, 255, 0.05);
        }
    </style>
@endpush

@section('content')
    <!-- Full Width Header with Background Image -->
    <section class="full-width-section header-bg py-16">
        <div class="header-content text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-4">{{ $order->venue_name }}</h1>
            <p class="text-gray-300 text-lg">{{ $order->venue_address }}</p>
        </div>
    </section>

    <!-- Payment Content Section -->
    <section class="full-width-section py-16 bg-gradient-to-b from-black to-gray-900">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Timer Alert - moved inside main content -->
            <div
                class="bg-[#4B4B4B] rounded-lg p-4 flex flex-col md:flex-row md:items-center md:justify-between gap-4 text-white mb-6">
                <!-- Kiri: Info -->
                <div class="flex items-start gap-3">
                    <div class="w-8 h-8 bg-kreen-gold bg-opacity-20 rounded-full flex items-center justify-center mt-1">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="text-sm">
                        <p class="font-medium leading-snug text-white">We are holding your ticket,<br class="md:hidden">
                            please finish your payment before</p>
                        <p class="font-bold mt-1 text-kreen-gold ">{{ $expired_at->format('d M Y, H:i') }}</p>
                    </div>
                </div>

                <!-- Kanan: Timer -->
                <div class="flex justify-center md:justify-end items-center gap-2">
                    <div class="bg-kreen-gold bg-opacity-20 rounded px-2 py-1 min-w-[44px] text-center">
                        <span class="text-white font-bold text-lg" id="hours">00</span>
                    </div>
                    <div class="bg-kreen-gold  bg-opacity-20 rounded px-2 py-1 min-w-[44px] text-center">
                        <span class="text-white font-bold text-lg" id="minutes">00</span>
                    </div>
                    <div class="bg-kreen-gold  bg-opacity-20 rounded px-2 py-1 min-w-[44px] text-center">
                        <span class="text-white font-bold text-lg" id="seconds">00</span>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-12 gap-12 booking-grid">
                <!-- Left Side - Payment Form (8 columns) -->
                <div class="lg:col-span-8 space-y-8">
                    <!-- Complete Payment -->
                    <div>
                        <h2 class="text-white text-2xl font-bold mb-6">Complete Payment</h2>

                        <!-- Bank Info -->
                        <div class="flex items-center space-x-3 mb-6">
                            <img src="{{ asset('image/payment-method/' . $order->img) }}" alt="{{ $order->payment_name }}"
                                class="w-12 h-12 object-contain bg-white p-1 rounded border border-gray-300" />
                            <span class="text-white font-medium text-lg">{{ $order->payment_name }}</span>
                        </div>


                        <!-- Virtual Account Number -->
                        {{-- //TODO: kondisikan juga untuk ewallet (ada button bayar skrg), QRIS (tampilkan QR), CC (Bayar sekarang), directdebit (Bayar sekarang), check status ditekan -> loading 1 layar gitu agak hitam -> muncul modal ( kalo belum bayar, kalo sudah bayar ke halaman success payment, kalo expired ke expired payment)  --}}
                        <div class="mb-6">
                            @if (env('APP_ENV') !== 'production')
                                <button
                                    onclick="simulatePayment('{{ $order->payment_request_id }}', '{{ $order->total_amount }}')"
                                    class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 text-lg">
                                    Bayar Sekarang
                                </button>
                            @endif
                            @if (!is_null($order->qr_string))
                                {{-- ✅ Tampilkan QRIS --}}
                                <div class="flex justify-center">
                                    <div class="bg-white p-3 rounded-xl shadow-lg border border-gray-300">
                                        <img src="https://api.qrserver.com/v1/create-qr-code/?data={{ urlencode($order->qr_string) }}&size=250x250"
                                            alt="QRIS Payment" class="w-64 h-64 object-contain" />
                                    </div>
                                </div>
                            @elseif (!is_null($order->va_number))
                                {{-- ✅ Tampilkan Virtual Account --}}
                                <label class="block text-white text-sm font-medium mb-2">Virtual Account Number</label>
                                <div class="flex items-center space-x-3">
                                    <div class="flex-1 bg-gray-800 border border-gray-600 rounded-lg px-4 py-3">
                                        <span class="text-white font-mono text-lg">{{ $order->va_number }}</span>
                                    </div>
                                    <button
                                        class="copy-btn px-4 py-3 border border-gray-600 rounded-lg text-gray-400 hover:text-white transition-colors"
                                        onclick="copyToClipboard(event, this, '{{ $order->va_number }}')">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                        </svg>
                                    </button>
                                </div>
                            @elseif (
                                !is_null($order->mobile_web_checkout_url) ||
                                    !is_null($order->desktop_web_checkout_url) ||
                                    !is_null($order->mobile_deeplink_checkout_url))
                                {{-- ✅ Tampilkan Tombol Bayar Sekarang --}}
                                <button
                                    onclick="redirectToPayNow('{{ $order->mobile_web_checkout_url ?? $order->desktop_web_checkout_url }}')"
                                    class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 text-lg">
                                    Bayar Sekarang
                                </button>
                            @endif
                        </div>


                        <!-- Total Payment -->
                        <div class="mb-6">
                            <label class="block text-white text-sm font-medium mb-2">Total Payment (IDR)</label>
                            <div class="flex items-center space-x-3">
                                <div class="flex-1 bg-gray-800 border border-gray-600 rounded-lg px-4 py-3">
                                    <span
                                        class="text-white font-bold text-xl">{{ \App\Helpers\Format::formatCurrency($order->total_amount, $order->currency) }}</span>
                                </div>
                                <button
                                    class="copy-btn px-4 py-3 border border-gray-600 rounded-lg text-gray-400 hover:text-white transition-colors"
                                    onclick="copyToClipboard(event, this, '{{ $order->total_amount }}')">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Booking Code -->
                        <div class="mb-8">
                            <label class="block text-white text-sm font-medium mb-2">Booking Code</label>
                            <div class="bg-gray-800 border border-gray-600 rounded-lg px-4 py-3">
                                <span class="text-white font-mono text-sm break-all">{{ $order->invoice_number }}</span>
                            </div>
                        </div>

                        <!-- Check Payment Status Button -->
                        <button onclick="checkPaymentStatus('{{ $order->id_order }}')"
                            class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 text-lg">
                            CHECK PAYMENT STATUS
                        </button>
                    </div>

                    <!-- Payment Method -->
                    <div>
                        <h2 class="text-white text-2xl font-bold mb-6">Payment Method</h2>

                        <div class="space-y-4">
                            <!-- I-Banking -->
                            @foreach ($instructions as $instruction)
                                <div class="border border-gray-600 rounded-lg">
                                    <button
                                        class="w-full flex items-center justify-between p-4 text-left hover:bg-gray-800 transition-colors"
                                        onclick="toggleDropdown('{{ $instruction->id }}')">
                                        <span
                                            class="text-white font-medium">{{ $instruction->metod_instruction_payget }}</span>
                                        <svg class="w-5 h-5 text-gray-400 transform transition-transform"
                                            id="{{ $instruction->id }}-arrow" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                    <div class="hidden border-t border-gray-600 p-4 bg-gray-800 bg-opacity-50"
                                        id="{{ $instruction->id }}-content">
                                        {!! $instruction->instruction_payget !!}
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Right Side - Booking Summary (4 columns) -->
                <div class="lg:col-span-4">
                    <div class="sticky-card border-2 border-kreen-gold rounded-xl p-6 space-y-6"
                        style="background: linear-gradient(98.07deg, #000000 0%, #2E2E2E 25.48%, #000000 50.48%, #464646 81.73%, #040404 100%);">
                        <h3 class="text-white text-xl font-bold mb-4">Your Booking</h3>

                        <!-- Venue -->
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-white font-medium text-sm">{{ $order->venue_name }}</p>
                            </div>
                        </div>

                        <!-- Date -->
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-gray-400 text-sm">
                                    {{ \Carbon\Carbon::parse($order->date)->format('l, d F Y') }}</p>
                            </div>
                        </div>

                        <!-- Ticket -->
                        <div class="bg-white/20 bg-opacity-50 rounded-lg p-4">
                            @foreach ($tickets as $ticket)
                                <div class="flex items-center space-x-3">
                                    <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                d="M2 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 100 4v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2a2 2 0 100-4V6z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-white text-sm font-medium">{{ $ticket['nama_tiket'] }}</p>
                                        <p class="text-gray-400 text-xs">{{ $ticket['qty'] }} Ticket x
                                            {{ \App\Helpers\Format::formatCurrency($ticket['harga_satuan'], $order->currency) }}
                                        </p>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Divider -->
                        <hr class="border-gray-600">

                        <!-- Payment Details -->
                        <div class="space-y-3">
                            <h4 class="text-white font-semibold">Payment Detail</h4>
                            @foreach ($tickets as $ticket)
                                <div class="flex justify-between">
                                    <span class="text-gray-400 text-sm">{{ $ticket['nama_tiket'] }}
                                        (x{{ $ticket['qty'] }})
                                    </span>
                                    <span
                                        class="text-white text-sm">{{ \App\Helpers\Format::formatCurrency($ticket['total_harga'], $order->currency) }}</span>
                                </div>
                            @endforeach

                            <div class="flex justify-between">
                                <span class="text-gray-400 text-sm">Service Fee</span>
                                <span
                                    class="text-white text-sm">{{ \App\Helpers\Format::formatCurrency($order->fees, 'IDR') }}</span>
                            </div>

                            <hr class="border-gray-600">

                            <div class="flex justify-between">
                                <span class="text-white font-medium">Total Payment</span>
                                <span
                                    class="text-kreen-gold font-bold text-lg">{{ \App\Helpers\Format::formatCurrency($order->total_amount, 'IDR') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Fullscreen Overlay -->
    <div id="payment-overlay" class="hidden">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p class="text-white text-lg font-medium">Memeriksa status pembayaran...</p>
            <p class="text-gray-300 text-sm mt-2">Mohon tunggu sebentar</p>
        </div>
    </div>

    <!-- Status Modal -->
    <div id="payment-modal" class="hidden">
        <div class="modal-content">
            <h2 id="payment-modal-title" class="text-2xl font-semibold mb-2"></h2>
            <p id="payment-modal-message" class="text-base mb-4"></p>
            <button onclick="closePaymentModal()"
                class="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-5 py-2 rounded-md font-semibold">
                Tutup
            </button>
        </div>
    </div>

@endsection

@push('scripts')
    <script>
        function checkPaymentStatus(orderId) {
            const overlay = document.getElementById('payment-overlay');
            const modal = document.getElementById('payment-modal');
            const modalTitle = document.getElementById('payment-modal-title');
            const modalMessage = document.getElementById('payment-modal-message');

            // Show overlay with proper display
            overlay.classList.remove('hidden');
            overlay.style.display = 'flex';

            // Disable body scroll
            document.body.style.overflow = 'hidden';
            const checkPaymentBaseUrl =
                "{{ route('order.checkStatusPayment', ['id_order' => ':id_order']) }}";
            const successPaymentBaseUrl = "{{ route('order.successPayment', ['id_order' => ':id_order']) }}";
            const expiredPaymentBaseUrl = "{{ route('order.expiredPayment', ['id_order' => ':id_order']) }}";
            const failedPaymentBaseUrl = "{{ route('order.failedPayment', ['id_order' => ':id_order']) }}";
            const checkPaymentUrl = checkPaymentBaseUrl.replace(':id_order', orderId);
            const successPaymentUrl = successPaymentBaseUrl.replace(':id_order', orderId);
            const expiredPaymentUrl = expiredPaymentBaseUrl.replace(':id_order', orderId);
            const failedPaymentUrl = failedPaymentBaseUrl.replace(':id_order', orderId);
            $.ajax({
                url: checkPaymentUrl,
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    // Hide overlay
                    overlay.classList.add('hidden');
                    overlay.style.display = 'none';
                    document.body.style.overflow = '';
                    if (response.success) {

                        if (response.data.status === '1') {
                            modalTitle.innerText = "Pembayaran Berhasil";
                            modalMessage.innerText =
                                "Terima kasih, pembayaran Anda telah berhasil. Anda akan diarahkan ke halaman detail pemesanan dalam 5 detik.";
                            modal.classList.remove('hidden');
                            modal.style.display = 'flex';
                            setTimeout(function() {
                                window.location.href = successPaymentUrl;
                            }, 5000);
                        } else if (response.data.status === '20') {
                            modalTitle.innerText = "Pembayaran Kadaluarsa";
                            modalMessage.innerText =
                                "Mohon maaf, pembayaran Anda telah kadaluarsa. Anda akan diarahkan ke halaman gagal bayar dalam 5 detik.";
                            modal.classList.remove('hidden');
                            modal.style.display = 'flex';
                            setTimeout(function() {
                                window.location.href = expiredPaymentUrl;
                            }, 5000);
                        } else if (response.data.status === '3') {
                            modalTitle.innerText = "Pembayaran Belum Diterima";
                            modalMessage.innerText =
                                "Silakan lakukan pembayaran terlebih dahulu, atau coba lagi dalam beberapa saat.";
                            modal.classList.remove('hidden');
                            modal.style.display = 'flex';
                        } else {
                            modalTitle.innerText = "Pesanan Gagal";
                            modalMessage.innerText = "Pesanan Anda gagal. Silakan coba lagi.";
                            modal.classList.remove('hidden');
                            modal.style.display = 'flex';
                        }
                    } else {
                        modalTitle.innerText = "Gagal Memeriksa Status";
                        modalMessage.innerText =
                        "Terjadi kesalahan saat menghubungi server. Silakan coba lagi.";
                        modal.classList.remove('hidden');
                        modal.style.display = 'flex';
                    }
                },
                error: function() {
                    // Hide overlay
                    overlay.classList.add('hidden');
                    overlay.style.display = 'none';
                    document.body.style.overflow = '';
                    modalTitle.innerText = "Gagal Memeriksa Status";
                    modalMessage.innerText = "Terjadi kesalahan saat menghubungi server. Silakan coba lagi.";
                    modal.classList.remove('hidden');
                    modal.style.display = 'flex';
                }
            });
        }

        function closePaymentModal() {
            const modal = document.getElementById('payment-modal');
            modal.classList.add('hidden');
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }
    </script>


    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Timer countdown
            function startTimer() {
                let diff = @json(now()->diffInSeconds($expired_at));
                console.log(diff);
                let hours = Math.floor(diff / 3600);
                let minutes = Math.floor((diff % 3600) / 60);
                let seconds = Math.floor(diff % 60);

                const timer = setInterval(() => {
                    seconds--;

                    if (seconds < 0) {
                        seconds = 59;
                        minutes--;

                        if (minutes < 0) {
                            minutes = 59;
                            hours--;

                            if (hours < 0) {
                                clearInterval(timer);
                                // Timer expired
                                window.location.reload();
                                return;
                            }
                        }
                    }

                    document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
                    document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
                    document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
                }, 1000);
            }

            startTimer();

            // Copy to clipboard function
            window.copyToClipboard = function(event, el, text) {
                navigator.clipboard.writeText(text).then(() => {
                    // Show success message
                    const originalText = el.innerHTML;
                    el.innerHTML =
                        '<svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/></svg>';

                    setTimeout(() => {
                        el.innerHTML = originalText;
                    }, 2000);
                }).catch((err) => {
                    console.log(err);
                    alert('Failed to copy to clipboard');
                });
            };

            // Toggle dropdown function
            window.toggleDropdown = function(id) {
                const content = document.getElementById(id + '-content');
                const arrow = document.getElementById(id + '-arrow');

                if (content.classList.contains('hidden')) {
                    content.classList.remove('hidden');
                    arrow.style.transform = 'rotate(180deg)';
                } else {
                    content.classList.add('hidden');
                    arrow.style.transform = 'rotate(0deg)';
                }
            };
        });

        function redirectToPayNow(url) {
            if (url && url !== '#') {
                window.location.href = url;
            } else {
                alert('Link pembayaran tidak tersedia.');
            }
        }

        // Header Navigation Replacement for Payment Flow (Step 2)
        function replaceHeaderNavigation() {
            // Hide existing navigation
            const desktopNav = document.querySelector('nav.hidden.md\\:flex');
            const loginButton = document.querySelector('a[href=""].hidden.md\\:inline-block');

            if (desktopNav) {
                desktopNav.style.display = 'none';
            }
            if (loginButton) {
                loginButton.style.display = 'none';
            }

            // Create progress indicator with step 2 active
            const headerContainer = document.querySelector('header .flex.items-center.justify-between');
            if (headerContainer && !document.querySelector('.booking-progress')) {
                const progressContainer = document.createElement('div');
                progressContainer.className = 'booking-progress flex items-center space-x-8';
                progressContainer.innerHTML = `
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <span class="text-green-400 font-medium text-sm">Guest Info</span>
            </div>

            <div class="flex items-center">
                <div class="w-8 h-0.5 bg-kreen-gold"></div>
                <svg class="w-3 h-3 text-kreen-gold mx-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
            </div>

            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-kreen-gold rounded-full flex items-center justify-center">
                    <span class="text-black text-sm font-bold">2</span>
                </div>
                <span class="text-kreen-gold font-medium text-sm">Payment</span>
            </div>

            <div class="flex items-center">
                <div class="w-8 h-0.5 bg-gray-600"></div>
                <svg class="w-3 h-3 text-gray-600 mx-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
            </div>

            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 border-2 border-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-gray-600 text-sm font-bold">3</span>
                </div>
                <span class="text-gray-600 font-medium text-sm">You're In!</span>
            </div>
        `;

                // Insert progress indicator after logo
                const logoContainer = headerContainer.querySelector('.flex.items-center');
                if (logoContainer && logoContainer.nextElementSibling) {
                    headerContainer.insertBefore(progressContainer, logoContainer.nextElementSibling);
                }
            }
        }

        // Initialize header replacement when page loads
        replaceHeaderNavigation();
        setTimeout(replaceHeaderNavigation, 100);

        // Add mobile progress indicator
        function addMobileProgress() {
            const mobileMenu = document.querySelector('#mobile-menu');
            if (mobileMenu && !document.querySelector('.booking-progress-mobile')) {
                const mobileProgress = document.createElement('div');
                mobileProgress.className =
                    'booking-progress-mobile md:hidden flex items-center justify-center space-x-4 px-4 py-3 bg-kreen-dark border-t border-gray-800';
                mobileProgress.innerHTML = `
            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <span class="text-green-400 font-medium text-[10px] md:text-xs">Info</span>
            </div>

            <svg class="w-3 h-3 text-kreen-gold" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>

            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 bg-kreen-gold rounded-full flex items-center justify-center">
                    <span class="text-black text-xs font-bold">2</span>
                </div>
                <span class="text-kreen-gold font-medium text-[10px] md:text-xs">Payment</span>
            </div>

            <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>

            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 border border-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-gray-600 text-xs font-bold">3</span>
                </div>
                <span class="text-gray-600 font-medium text-[10px] md:text-xs">In!</span>
            </div>
        `;

                mobileMenu.parentNode.insertBefore(mobileProgress, mobileMenu.nextSibling);
            }
        }

        addMobileProgress();
        setTimeout(addMobileProgress, 100);

        function simulatePayment(payment_request_id, amount) {
            $.ajax({
                url: @json(route('xendit.simulatePayment')),
                type: 'POST',
                dataType: 'json',
                data: {
                    _token: '{{ csrf_token() }}',
                    payment_request_id: payment_request_id,
                    amount: amount,
                },
                success: function(response) {
                    if (response.status === 'success') {
                        window.location.href = response.url;
                    }
                },
                error: function(response) {
                    console.log(response);
                }
            })
        }
    </script>
@endpush
