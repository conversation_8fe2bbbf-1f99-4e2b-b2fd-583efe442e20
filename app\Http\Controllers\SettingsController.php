<?php

namespace App\Http\Controllers;

use App\Helpers\ApiHelper;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;


class SettingsController extends Controller
{
    public function index()
    {

        $user = Auth::user();

        // dd($user);

        return view('vip.settings.settings', compact('user'));
    }

    public function updatePersonalInfo(Request $request)
    {
        $userId = (string) Auth::user()->id;
        
        if (!$userId) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated.'
            ], 401);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|min:2',
            'gender' => 'required|in:male,female',
            'phone' => 'nullable|string|max:20',
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $updateData = [
                'name' => $request->name,
                'gender' => $request->gender,
                'phone' => $request->phone,
                'updated_at' => now()
            ];

            // Handle profile picture upload
            if ($request->hasFile('profile_picture')) {
                $file = $request->file('profile_picture');
                $fileName = 'profile_' . $userId . '_' . time() . '.' . $file->getClientOriginalExtension();
                
                // Store in public/storage/profile_pictures
                $path = $file->storeAs('profile_pictures', $fileName, 'public');
                $updateData['profile_picture'] = $path;

                // Delete old profile picture if exists
                $oldUser = DB::table('tbl_users')->where('id', $userId)->first();
                if ($oldUser && isset($oldUser->profile_picture) && $oldUser->profile_picture && Storage::disk('public')->exists($oldUser->profile_picture)) {
                    Storage::disk('public')->delete($oldUser->profile_picture);
                }
            }

            $updated = DB::table('tbl_users')
                ->where('id', $userId)
                ->update($updateData);

            // \Log::info('Update result:', ['updated' => $updated]);

            if ($updated) {
                return response()->json([
                    'success' => true,
                    'message' => 'Personal information updated successfully!'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update personal information.'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating personal information.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function updatePassword(Request $request)
    {
        $userId = Auth::id();
        
        if (!$userId) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated.'
            ], 401);
        }

        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'new_password' => ['required', 'string', 'min:8'],
            'confirm_password' => 'required|string|same:new_password'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = DB::table('tbl_users')->where('id', '=', $userId)->first();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found.'
                ], 404);
            }

            // Check if current password is correct
            if (!Hash::check($request->current_password, $user->password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Current password is incorrect.'
                ], 422);
            }

            // Update password
            $updated = DB::table('tbl_users')
                ->where('id', '=', $userId)
                ->update([
                    'password' => Hash::make($request->new_password),
                    'updated_at' => now()
                ]);

            if ($updated) {
                return response()->json([
                    'success' => true,
                    'message' => 'Password updated successfully!'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update password.'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating password.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function updateEmail(Request $request)
    {
        $userId = Auth::id();
        
        if (!$userId) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated.'
            ], 401);
        }

        $validator = Validator::make($request->all(), [
            'new_email' => 'required|email|unique:tbl_users,email,' . $userId . ',id',
            'confirm_password' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = DB::table('tbl_users')->where('id', '=', $userId)->first();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found.'
                ], 404);
            }

            // Check if password is correct
            if (!Hash::check($request->confirm_password, $user->password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Password is incorrect.'
                ], 422);
            }

            // Update email and reset email verification
            $updated = DB::table('tbl_users')
                ->where('id', '=', $userId)
                ->update([
                    'email' => $request->new_email,
                    'email_verified_at' => null, // Reset email verification
                    'updated_at' => now()
                ]);

            if ($updated) {
                return response()->json([
                    'success' => true,
                    'message' => 'Email updated successfully! Please verify your new email address.'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update email.'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating email.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getUserData()
    {
        $userId = Auth::id();
        
        if (!$userId) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated.'
            ], 401);
        }

        try {
            $user = DB::table('tbl_users')
                ->select('id', 'name', 'email', 'gender', 'phone', 'profile_picture', 'email_verified_at')
                ->where('id', '=', $userId)
                ->first();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found.'
                ], 404);
            }

            // Add profile picture URL if exists
            if (isset($user->profile_picture) && $user->profile_picture) {
                $user->profile_picture_url = Storage::url($user->profile_picture);
            } else {
                $user->profile_picture_url = null;
            }

            return response()->json([
                'success' => true,
                'data' => $user
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching user data.',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
