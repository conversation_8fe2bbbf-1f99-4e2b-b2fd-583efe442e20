<?php

namespace App\Services\PaymentGateway;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

interface PaymentGatewayInterface
{
    public function createTransaction(array $payload): array|JsonResponse;

    // public function getTransaction(string $externalId): array;

    public function handleCallback(string $event, array $data): JsonResponse;

    public function simulatePayment(Request $request): JsonResponse;
}
