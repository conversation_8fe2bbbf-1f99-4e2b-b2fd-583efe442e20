<?php $__env->startSection('title', $venue['nama']); ?>

<?php $__env->startPush('styles'); ?>
    <style>
        .sticky-card {
            position: sticky;
            top: 10rem;
            z-index: 10;
        }

        /* Ensure parent containers don't interfere */
        .booking-container {
            position: relative;
            overflow: visible;
        }

        .booking-grid {
            position: relative;
            overflow: visible;
        }

        /* Mobile sticky adjustments */
        @media (max-width: 1024px) {
            .sticky-card {
                position: relative;
                top: auto;
            }
        }

        #map {
            height: 326px;
            border-radius: 12px;
        }

        /* LightGallery Custom Styles */
        .gallery-item {
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .gallery-item:hover {
            transform: scale(1.05);
        }

        /* Hidden gallery for lightbox */
        #hidden-gallery {
            display: none;
        }

        .hidden-on-initial-load {
            display: none;
        }
    </style>

    <!-- LightGallery CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/css/lightgallery.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/css/lg-zoom.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/css/lg-thumbnail.css">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Container dengan Background Image untuk Venue Detail -->
    <div class="explore-nights-bg-container">
        <!-- Venue Hero Section -->
        <section class="explore-nights-content py-8">
            <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Venue Header Image -->
                <div class="mb-8">
                    <div class="rounded-xl overflow-hidden h-64 md:h-80"
                        style="background-image: url('<?php echo e(asset($venue['banner'])); ?>'); background-size: cover; background-position: center;">
                    </div>
                </div>

                <!-- Welcome Section -->
                <div class="text-center mb-12">
                    <p class="text-gray-400 text-lg mb-2">WELCOME TO</p>
                    <h1 class="text-4xl md:text-5xl font-bold text-white mb-8 uppercase"><?php echo e($venue['nama']); ?></h1>
                </div>
            </div>
        </section>

        <!-- Booking Section -->
        <section class="explore-nights-content py-12 booking-container">
            <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-12 gap-12 booking-grid">
                    <!-- <div class="grid grid-cols-1 lg:grid-cols-12 gap-12 booking-grid"> -->
                    <!-- Left Side - Form (8 columns) -->
                    <div class="<?php echo e($venue['flag_sale'] == 1 ? 'lg:col-span-8' : 'lg:col-span-12'); ?> space-y-8">

                        <!-- Check if any upcoming events data -->
                        <?php if($upcoming_events): ?>
                            <div class="mb-8">
                                <h2 class="text-white text-xl font-semibold mb-4">Upcoming Event</h2>
                                <div
                                    class="flex items-center bg-transparent bg-opacity-40 border border-yellow-500 rounded-2xl p-4 space-x-4">
                                    <img src="<?php echo e($upcoming_events->gambar); ?>" alt="<?php echo e($upcoming_events->nama); ?>"
                                        class="w-24 h-24 rounded-xl object-cover flex-shrink-0" />

                                    <div>
                                        <h3 class="text-white font-bold text-lg mb-1">
                                            <?php echo e(strtoupper($upcoming_events->nama)); ?> 🔥
                                        </h3>
                                        <p class="text-gray-300 text-sm">
                                            <?php echo e(\Carbon\Carbon::parse($upcoming_events->tanggal)->format('l, d F Y')); ?>

                                            · Showtime:
                                            <?php echo e(\Carbon\Carbon::parse($upcoming_events->tanggal)->format('h:i A')); ?>

                                        </p>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        <!-- End upcoming -->

                        <?php if($venue['flag_sale'] == 1): ?>
                            <div class="relative">
                                <input type="date" placeholder="Choose Date" value="<?php echo e(date('Y-m-d')); ?>"
                                    onchange="getTicketsByDate()"
                                    class="w-full bg-transparent border-2 border-kreen-gold text-white px-4 py-4 rounded-2xl focus:outline-none focus:ring-2 focus:ring-kreen-gold placeholder-gray-400 appearance-none">

                                <!-- Custom calendar icon (putih) -->
                                <div class="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            </div>


                            <!-- First Drink Charge Ticket -->
                            <div>
                                <h2 class="text-white text-xl font-semibold mb-4">First Drink Charge Ticket</h2>

                                <div id="list-ticket-from-ajax" class="flex flex-col gap-6 rounded-2xl"></div>
                            </div>
                        <?php endif; ?>

                        <div class="border-2 border-kreen-gold rounded-2xl p-6 bg-transparent space-y-8">
                            <div>
                                <h2 class="text-white text-xl font-semibold mb-4">About</h2>
                                <h3 class="text-white font-semibold text-lg mb-4 uppercase5"><?php echo e($venue['nama']); ?></h3>
                                <p class="text-gray-300 text-sm leading-relaxed mb-4">
                                    <?php echo $venue['informasi']; ?>

                                </p>
                            </div>
                            <?php if($venue['open_hour']->count() > 0): ?>
                                <hr class="border-gray-600">
                                <div>
                                    <h2 class="text-white text-xl font-semibold mb-4">Open Hours</h2>
                                    <div class="space-y-3">
                                        <?php $__empty_1 = true; $__currentLoopData = $venue['open_hour']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $open_hour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-3">
                                                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                                    <span class="text-white text-sm">
                                                        <?php echo e(\Carbon\Carbon::create()->startOfWeek(\Carbon\Carbon::MONDAY)->addDays($open_hour['hari'] - 1)->translatedFormat('l')); ?>

                                                    </span>
                                                </div>
                                                <span
                                                    class="text-gray-300 text-sm"><?php echo e(\Carbon\Carbon::parse($open_hour['jam_buka'])->format('H:i')); ?>

                                                    -
                                                    <?php echo e(\Carbon\Carbon::parse($open_hour['jam_tutup'])->format('H:i')); ?></span>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <?php if($venue['lat'] != 0 && $venue['lng'] != 0): ?>
                                <hr class="border-gray-600">
                                <div>
                                    <h2 class="text-white text-xl font-semibold mb-4">Location</h2>
                                    <div id="map" class="mb-3"></div>
                                    <div class="flex items-start space-x-3">
                                        <div class="w-5 h-5 flex items-center justify-center flex-shrink-0 mt-0.5">
                                            <svg class="w-4 h-4 text-kreen-gold" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                                    clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-gray-300 text-sm"><?php echo e($venue['alamat']); ?></p>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <hr class="border-gray-600">
                            <div>
                                <h2 class="text-white text-xl font-semibold mb-4">Gallery</h2>
                                <div class="grid grid-cols-3 gap-3" id="gallery-grid">
                                    <?php
                                        $is_show_more = count($galleries) > 3;
                                        $initial_display_limit = $is_show_more ? 2 : 3; // Tampilkan 2 jika ada "See All Photos", jika tidak, tampilkan hingga 3
                                    ?>
                                    <?php $__empty_1 = true; $__currentLoopData = $galleries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $idx => $gallery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <!-- Gallery Image (semua sekarang adalah tag <a>) -->
                                        <a class="aspect-square rounded-lg overflow-hidden full-gallery-link <?php echo e($idx >= $initial_display_limit ? 'hidden-on-initial-load' : ''); ?>"
                                            href="<?php echo e(strpos($gallery->gambar, 'http') === 0 ? $gallery->gambar : asset($gallery->gambar)); ?>"
                                            data-sub-html="<h4><?php echo e($venue['nama']); ?> - Gallery <?php echo e($idx + 1); ?></h4>"
                                            data-lg-size="1600-1067" 
                                            data-exthumbimage="<?php echo e(strpos($gallery->gambar, 'http') === 0 ? $gallery->gambar : asset($gallery->gambar)); ?>">
                                            <div class="w-full h-full bg-gradient-to-br from-amber-900 to-amber-700"
                                                style="background-image: url('<?php echo e(strpos($gallery->gambar, 'http') === 0 ? $gallery->gambar : asset($gallery->gambar)); ?>'); background-size: cover; background-position: center;">
                                            </div>
                                        </a>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <div class="aspect-square rounded-lg overflow-hidden">
                                            <div class="w-full h-full bg-gradient-to-br from-amber-900 to-amber-700"></div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($is_show_more): ?>
                                        <!-- See All Photos Button -->
                                        <div id="see-all-photos-btn"
                                            class="aspect-square rounded-lg overflow-hidden bg-gray-800 bg-opacity-50 flex items-center justify-center cursor-pointer hover:bg-opacity-70 transition-all">
                                            <div class="text-center">
                                                <svg class="w-8 h-8 text-kreen-gold mx-auto mb-2" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                                <p class="text-white text-xs font-medium">See All Photos</p>
                                                <p class="text-gray-400 text-xs"><?php echo e(count($galleries)); ?> photos</p>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                            </div>

                            <!-- Divider -->
                            <hr class="border-gray-600">

                            <!-- Contact Us Section -->
                            <div>
                                <h2 class="text-white text-xl font-semibold mb-4">Contact Us</h2>
                                <div class="flex flex-col md:flex-row md:items-center md:space-x-8 space-y-4 md:space-y-0">

                                    
                                    <?php if(!empty($venue['username_ig'])): ?>
                                        <div class="flex items-center space-x-3">
                                            <div class="w-5 h-5 flex items-center justify-center flex-shrink-0">
                                                <!-- IG Icon -->
                                                <svg class="w-4 h-4 text-kreen-gold" fill="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path
                                                        d="M12 2.163c3.204 0 3.584.012 4.85.07 1.366.062 2.633.326 3.608 1.301.975.975 1.24 2.242 1.301 3.608.058 1.266.07 1.646.07 4.85s-.012 3.584-.07 4.85c-.062 1.366-.326 2.633-1.301 3.608-.975.975-2.242 1.24-3.608 1.301-1.266.058-1.646.07-4.85.07s-3.584-.012-4.85-.07c-1.366-.062-2.633-.326-3.608-1.301-.975-.975-1.24-2.242-1.301-3.608-.058-1.266-.07-1.646-.07-4.85s.012-3.584.07-4.85c.062-1.366.326-2.633 1.301-3.608.975-.975 2.242-1.24 3.608-1.301 1.266-.058 1.646-.07 4.85-.07zm0-2.163c-3.259 0-3.667.014-4.947.072-1.634.074-3.082.414-4.255 1.587C1.586 2.831 1.246 4.279 1.172 5.913c-.058 1.28-.072 1.688-.072 4.947s.014 3.667.072 4.947c.074 1.634.414 3.082 1.587 4.255 1.173 1.173 2.621 1.513 4.255 1.587 1.28.058 1.688.072 4.947.072s3.667-.014 4.947-.072c1.634-.074 3.082-.414 4.255-1.587 1.173-1.173 1.513-2.621 1.587-4.255.058-1.28.072-1.688.072-4.947s-.014-3.667-.072-4.947c-.074-1.634-.414-3.082-1.587-4.255-1.173-1.173-2.621-1.513-4.255-1.587-1.28-.058-1.688-.072-4.947-.072z" />
                                                    <path
                                                        d="M12 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zm0 10.162a4 4 0 110-8 4 4 0 010 8z" />
                                                    <path d="M16.335 5.663a1.44 1.44 0 11-2.88 0 1.44 1.44 0 012.88 0z" />
                                                </svg>
                                            </div>
                                            <a href="https://www.instagram.com/<?php echo e($venue['username_ig']); ?>"
                                                target="_blank" rel="noopener noreferrer" class="text-gray-300 text-sm">
                                                <?php echo e($venue['username_ig']); ?>

                                            </a>
                                        </div>
                                    <?php endif; ?>

                                    
                                    <?php if(!empty($venue['no_wa'])): ?>
                                        <div class="flex items-center space-x-3">
                                            <div class="w-5 h-5 flex items-center justify-center flex-shrink-0">
                                                <!-- WA Icon -->
                                                <svg class="w-4 h-4 text-kreen-gold" fill="currentColor"
                                                    viewBox="0 0 20 20">
                                                    <path
                                                        d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                                </svg>
                                            </div>
                                            <a href="https://wa.me/<?php echo e($venue['no_wa']); ?>" target="_blank"
                                                rel="noopener noreferrer" class="text-gray-300 text-sm">
                                                <?php echo e($venue['no_wa']); ?>

                                            </a>
                                        </div>
                                    <?php endif; ?>

                                </div>
                            </div>

                        </div>
                    </div>

                    <?php if($venue['flag_sale'] == 1): ?>
                        <div class="lg:col-span-4">
                            <div class="border-2 border-kreen-gold rounded-xl p-6 space-y-6"
                                style="background: linear-gradient(98.07deg, #000000 0%, #2E2E2E 25.48%, #000000 50.48%, #464646 81.73%, #040404 100%); position: sticky; top: 137px;">
                                <!-- Location -->
                                <div class="flex items-center space-x-3">
                                    <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-white font-medium text-sm"><?php echo e($venue['nama']); ?></p>
                                    </div>
                                </div>

                                <!-- Date -->
                                <div class="flex items-center space-x-3">
                                    <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-gray-400 text-sm" id="selected-date">Chosen date will be added on
                                            here
                                        </p>
                                    </div>
                                </div>

                                <!-- Ticket - with darker background -->
                                <div class="bg-white/20 bg-opacity-50 rounded-lg p-4">
                                    <div class="flex items-center space-x-3">

                                        <div id="selected-ticket-container" class="space-y-4">
                                            <!-- Kartu tiket akan ditambahkan di sini secara dinamis -->
                                            <p class="text-gray-400 text-sm" id="selected-ticket-default"
                                                style="display: block;">Chosen ticket will be added on here</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Divider -->
                                <hr class="border-gray-600">

                                <!-- Total -->
                                <div class="flex justify-between items-center">
                                    <span class="text-white font-medium" id="total-text">Total (0 ticket)</span>
                                    <span class="text-white font-bold text-lg" id="total-price">Rp 0</span>
                                </div>

                                <!-- Buy Button -->
                                <button type="button" onclick="continueCheckout()"
                                    class="w-full bg-kreen-gold hover:bg-yellow-500 text-black font-bold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 text-lg">
                                    BUY NOW
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </section>
    </div>

    

    <form action="<?php echo e(route('order.guestInfo')); ?>" id="form-guest-info" method="POST">
        <?php echo csrf_field(); ?>
        <input type="hidden" name="id_venue" id="hidden-id-venue">
        <input type="hidden" name="date" id="hidden-date">
        <input type="hidden" name="tickets" id="hidden-tickets">
    </form>
<?php $__env->stopSection(); ?>

<?php if($errors->any()): ?>
    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="alert alert-danger">
            <?php echo e($error); ?>

        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
    <!-- LightGallery JS -->
    <script src="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/lightgallery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/plugins/zoom/lg-zoom.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/plugins/thumbnail/lg-thumbnail.min.js"></script>



    <script>
        const venueId = <?php echo json_encode($venue['id'], 15, 512) ?>;
        const lat = <?php echo json_encode($venue['lat'] ?? 0, 15, 512) ?>;
        const lng = <?php echo json_encode($venue['lng'] ?? 0, 15, 512) ?>;

        // LightGallery Initialization
        document.addEventListener('DOMContentLoaded', function() {
            const galleryGrid = document.getElementById('gallery-grid');
            const seeAllBtn = document.getElementById('see-all-photos-btn');

            if (galleryGrid) {
                // Inisialisasi LightGallery hanya sekali pada kontainer utama
                const lgInstance = lightGallery(galleryGrid, {
                    selector: '.full-gallery-link', // Target semua link dengan kelas ini
                    plugins: [lgZoom, lgThumbnail],
                    speed: 500,
                    thumbnail: true,
                    animateThumb: true,
                    zoomFromOrigin: false,
                    allowMediaOverlap: true,
                    toggleThumb: true,
                    download: false,
                    counter: true,
                    getCaptionFromTitleOrAlt: false,
                    showThumbByDefault: true,
                    exThumbImage: 'data-exthumbimage' // Pastikan ini sesuai dengan atribut data-exthumbimage di HTML
                });

                // "See All Photos" button click handler
                if (seeAllBtn) {
                    seeAllBtn.addEventListener('click', function() {
                        lgInstance.openGallery(0); // Buka galeri mulai dari gambar pertama
                    });
                }
            }
        });

        function getTicketsByDate() {
            const date = document.querySelector('input[type="date"]').value;
            const selectedDate = document.querySelector('#selected-date');
            const dateObject = new Date(date);
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            const formattedDate = dateObject.toLocaleDateString('id-ID', options);
            selectedDate.textContent = formattedDate;
            $.ajax({
                url: `<?php echo e(route('venue.ajax.getTickets')); ?>`,
                type: 'GET',
                data: {
                    date: date,
                    venue_id: venueId
                },
                success: function(response) {
                    $('#list-ticket-from-ajax').html(response);
                },
                error: function(error) {
                    console.log(error);
                }
            });

        }

        getTicketsByDate();

        function initMap() {
            const position = {
                lat: lat,
                lng: lng
            };
            const map = new google.maps.Map(document.getElementById("map"), {
                center: position,
                zoom: 16,
                mapId: "DEMO_MAP_ID",
                disableDefaultUI: true,
                zoomControl: true,
                fullscreenControl: true,
            });
            const marker = new google.maps.marker.AdvancedMarkerElement({
                position: position,
                title: 'Lokasi',
                map: map,
            });
        }

        function continueCheckout() {
            const dataTickets = [];
            const date = document.querySelector('input[type="date"]').value;
            const tickets = $(".tickets").filter(function() {
                return parseInt($(this).find('.quantity-input').val()) > 0;
            });
            tickets.each(function() {
                const ticketId = $(this).data('id-ticket');
                const ticketQuantity = parseInt($(this).find('.quantity-input').val());
                dataTickets.push({
                    id: ticketId,
                    qty: ticketQuantity
                });
            })
            const dataInput = {
                id_venue: venueId,
                date: date,
                tickets: dataTickets
            }
            $("#hidden-id-venue").val(venueId);
            $("#hidden-date").val(date);
            $("#hidden-tickets").val(JSON.stringify(dataTickets));
            $("#form-guest-info").submit();
        }
    </script>


    <script
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDsALC8ufBkAAp_aH6fvCFKkK-Mi0oneaA&callback=initMap&loading=async&region=ID&language=<?php echo e(app()->getLocale()); ?>&libraries=marker"
        defer></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('vip.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\vipkreen\kreenvip-new\resources\views/vip/venue/detail_venue.blade.php ENDPATH**/ ?>